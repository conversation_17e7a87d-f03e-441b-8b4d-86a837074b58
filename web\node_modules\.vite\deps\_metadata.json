{"hash": "59c8b36d", "configHash": "fe55a034", "lockfileHash": "407b6c0c", "browserHash": "a0ce92e1", "optimized": {"react": {"src": "../../.pnpm/react@19.1.1/node_modules/react/index.js", "file": "react.js", "fileHash": "6c925d7b", "needsInterop": true}, "react-dom": {"src": "../../.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "8d24f6e9", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../.pnpm/react@19.1.1/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d642d5f0", "needsInterop": true}, "react/jsx-runtime": {"src": "../../.pnpm/react@19.1.1/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4d354bab", "needsInterop": true}, "@tanstack/react-router": {"src": "../../.pnpm/@tanstack+react-router@1.13_5609a771cca355b79c209929dd504530/node_modules/@tanstack/react-router/dist/esm/index.js", "file": "@tanstack_react-router.js", "fileHash": "26f29db8", "needsInterop": false}, "@tanstack/react-router-devtools": {"src": "../../.pnpm/@tanstack+react-router-devt_93962342148034dfae2178ce6058ba2c/node_modules/@tanstack/react-router-devtools/dist/esm/index.js", "file": "@tanstack_react-router-devtools.js", "fileHash": "83da0043", "needsInterop": false}, "react-dom/client": {"src": "../../.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d5091f1d", "needsInterop": true}}, "chunks": {"FloatingTanStackRouterDevtools-7YM63SKD": {"file": "FloatingTanStackRouterDevtools-7YM63SKD.js"}, "chunk-UEWHOQAH": {"file": "chunk-UEWHOQAH.js"}, "chunk-CMYK4I76": {"file": "chunk-CMYK4I76.js"}, "chunk-THFEYB23": {"file": "chunk-THFEYB23.js"}, "chunk-V7IX5Q3P": {"file": "chunk-V7IX5Q3P.js"}, "BaseTanStackRouterDevtoolsPanel-YWFD7WPZ": {"file": "BaseTanStackRouterDevtoolsPanel-YWFD7WPZ.js"}, "chunk-JIWXKZKK": {"file": "chunk-JIWXKZKK.js"}, "chunk-LYANGJ2V": {"file": "chunk-LYANGJ2V.js"}, "chunk-KAR57YZN": {"file": "chunk-KAR57YZN.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}