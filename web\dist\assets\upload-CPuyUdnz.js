import{r as s,j as a}from"./index-PC516Tgl.js";function m(){const[o,c]=s.useState(),[d,r]=s.useState(!1),[u,n]=s.useState(),[f,l]=s.useState(!1),i=t=>{var e;c((e=t.target.files)==null?void 0:e[0])},p=async()=>{if(!o)return;const t=new FormData;t.append("file",o);try{r(!0),n(void 0),l(!1);const e=await fetch("/api/files/upload",{method:"POST",body:t,headers:{Accept:"application/json"},mode:"cors"});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);l(!0)}catch(e){n(e.message)}finally{r(!1)}};return a.jsxs("div",{children:[a.jsx("h2",{children:"Upload a File"}),a.jsx("input",{type:"file",onChange:i}),a.jsx("button",{onClick:p,children:"Upload"})]})}export{m as component};
