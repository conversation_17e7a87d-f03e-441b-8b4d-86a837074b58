import{r as s,j as n}from"./index-PC516Tgl.js";function l(){const[t,r]=s.useState();return s.useEffect(()=>{fetch("/api/files").then(e=>e.json()).then(e=>r(e))},[]),n.jsx("div",{style:{padding:"20px"},children:n.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx("h2",{children:"File Share"}),n.jsx("ul",{children:(t==null?void 0:t.length)===0||!t?n.jsx("li",{children:"No files found"}):t==null?void 0:t.map(e=>n.jsxs("li",{children:[e.isDir?"📁":"📄"," ",n.jsx("a",{href:e.path,target:"_blank",rel:"noreferrer",children:e.name})," ",!e.isDir&&`(${(e.size/1024).toFixed(1)} KB)`]},e.path))})]})})}export{l as component};
