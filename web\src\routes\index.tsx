import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import type { File } from '../types/file'
import '../App.css'

export const Route = createFileRoute('/')({
  component: Index,
})

function Index() {
  const [files, setFiles] = useState<File[] | undefined>()

  useEffect(() => {
    fetch("/api/files")
      .then((res) => res.json())
      .then((data: File[]) => setFiles(data));
  }, []);
    
  return (
    <div style={{ padding: "20px" }}>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
        <h2>File Share</h2>
        <ul>
          { files?.length === 0 || !files ? (
            <li>No files found</li>
          ) : (
            files?.map((f: File) => (
              <li key={f.path}>
                {f.isDir ? "📁" : "📄"}{" "}
                <a href={f.path} target="_blank" rel="noreferrer">
                  {f.name}
                </a>{" "}
                {!f.isDir && `(${(f.size / 1024).toFixed(1)} KB)`}
              </li>
            ))  
          )}
        </ul>
      </div>
    </div>
  )
}