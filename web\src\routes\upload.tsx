
import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'

export const Route = createFileRoute('/upload')({
  component: Upload,
})

function Upload() {
    const [file, setFile] = useState<any>()
    const [uploading, setUploading] = useState(false)
    const [uploadError, setUploadError] = useState<string | undefined>()
    const [uploadSuccess, setUploadSuccess] = useState(false)

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setFile(event.target.files?.[0])
    }

    const handleUpload = async () => {
        if (!file) return

        const formData = new FormData()
        formData.append('file', file)

        try {
            setUploading(true)
            setUploadError(undefined)
            setUploadSuccess(false)

            const res = await fetch('/api/files/upload', {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
                mode: 'cors',
            })
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`)
            }

            setUploadSuccess(true)
        }
        catch (error) {
            setUploadError((error as Error).message)
        }
        finally {
            setUploading(false)
        }
    }
    
    return (
        <div>
            <h2>Upload a File</h2>
            <input type="file" onChange={handleFileChange} />
            <button onClick={handleUpload}>Upload</button>
        </div>
    )
}