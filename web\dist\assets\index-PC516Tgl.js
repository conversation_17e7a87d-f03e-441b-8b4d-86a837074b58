(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))f(r);new MutationObserver(r=>{for(const d of r)if(d.type==="childList")for(const v of d.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&f(v)}).observe(document,{childList:!0,subtree:!0});function c(r){const d={};return r.integrity&&(d.integrity=r.integrity),r.referrerPolicy&&(d.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?d.credentials="include":r.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function f(r){if(r.ep)return;r.ep=!0;const d=c(r);fetch(r.href,d)}})();function fm(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var ls={exports:{}},tu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dh;function wv(){if(Dh)return tu;Dh=1;var i=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function c(f,r,d){var v=null;if(d!==void 0&&(v=""+d),r.key!==void 0&&(v=""+r.key),"key"in r){d={};for(var p in r)p!=="key"&&(d[p]=r[p])}else d=r;return r=d.ref,{$$typeof:i,type:f,key:v,ref:r!==void 0?r:null,props:d}}return tu.Fragment=s,tu.jsx=c,tu.jsxs=c,tu}var xh;function Xv(){return xh||(xh=1,ls.exports=wv()),ls.exports}var Q=Xv(),as={exports:{}},ct={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ch;function Qv(){if(Ch)return ct;Ch=1;var i=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),r=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),v=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),g=Symbol.iterator;function _(R){return R===null||typeof R!="object"?null:(R=g&&R[g]||R["@@iterator"],typeof R=="function"?R:null)}var M={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},A=Object.assign,U={};function H(R,q,w){this.props=R,this.context=q,this.refs=U,this.updater=w||M}H.prototype.isReactComponent={},H.prototype.setState=function(R,q){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,q,"setState")},H.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function j(){}j.prototype=H.prototype;function at(R,q,w){this.props=R,this.context=q,this.refs=U,this.updater=w||M}var X=at.prototype=new j;X.constructor=at,A(X,H.prototype),X.isPureReactComponent=!0;var J=Array.isArray,V={H:null,A:null,T:null,S:null,V:null},lt=Object.prototype.hasOwnProperty;function et(R,q,w,G,k,st){return w=st.ref,{$$typeof:i,type:R,key:q,ref:w!==void 0?w:null,props:st}}function nt(R,q){return et(R.type,q,void 0,void 0,void 0,R.props)}function ht(R){return typeof R=="object"&&R!==null&&R.$$typeof===i}function Z(R){var q={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(w){return q[w]})}var it=/\/+/g;function mt(R,q){return typeof R=="object"&&R!==null&&R.key!=null?Z(""+R.key):q.toString(36)}function Gt(){}function Dt(R){switch(R.status){case"fulfilled":return R.value;case"rejected":throw R.reason;default:switch(typeof R.status=="string"?R.then(Gt,Gt):(R.status="pending",R.then(function(q){R.status==="pending"&&(R.status="fulfilled",R.value=q)},function(q){R.status==="pending"&&(R.status="rejected",R.reason=q)})),R.status){case"fulfilled":return R.value;case"rejected":throw R.reason}}throw R}function Lt(R,q,w,G,k){var st=typeof R;(st==="undefined"||st==="boolean")&&(R=null);var W=!1;if(R===null)W=!0;else switch(st){case"bigint":case"string":case"number":W=!0;break;case"object":switch(R.$$typeof){case i:case s:W=!0;break;case b:return W=R._init,Lt(W(R._payload),q,w,G,k)}}if(W)return k=k(R),W=G===""?"."+mt(R,0):G,J(k)?(w="",W!=null&&(w=W.replace(it,"$&/")+"/"),Lt(k,q,w,"",function(ye){return ye})):k!=null&&(ht(k)&&(k=nt(k,w+(k.key==null||R&&R.key===k.key?"":(""+k.key).replace(it,"$&/")+"/")+W)),q.push(k)),1;W=0;var Zt=G===""?".":G+":";if(J(R))for(var gt=0;gt<R.length;gt++)G=R[gt],st=Zt+mt(G,gt),W+=Lt(G,q,w,st,k);else if(gt=_(R),typeof gt=="function")for(R=gt.call(R),gt=0;!(G=R.next()).done;)G=G.value,st=Zt+mt(G,gt++),W+=Lt(G,q,w,st,k);else if(st==="object"){if(typeof R.then=="function")return Lt(Dt(R),q,w,G,k);throw q=String(R),Error("Objects are not valid as a React child (found: "+(q==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":q)+"). If you meant to render a collection of children, use an array instead.")}return W}function C(R,q,w){if(R==null)return R;var G=[],k=0;return Lt(R,G,"","",function(st){return q.call(w,st,k++)}),G}function Y(R){if(R._status===-1){var q=R._result;q=q(),q.then(function(w){(R._status===0||R._status===-1)&&(R._status=1,R._result=w)},function(w){(R._status===0||R._status===-1)&&(R._status=2,R._result=w)}),R._status===-1&&(R._status=0,R._result=q)}if(R._status===1)return R._result.default;throw R._result}var P=typeof reportError=="function"?reportError:function(R){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof R=="object"&&R!==null&&typeof R.message=="string"?String(R.message):String(R),error:R});if(!window.dispatchEvent(q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",R);return}console.error(R)};function St(){}return ct.Children={map:C,forEach:function(R,q,w){C(R,function(){q.apply(this,arguments)},w)},count:function(R){var q=0;return C(R,function(){q++}),q},toArray:function(R){return C(R,function(q){return q})||[]},only:function(R){if(!ht(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},ct.Component=H,ct.Fragment=c,ct.Profiler=r,ct.PureComponent=at,ct.StrictMode=f,ct.Suspense=y,ct.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=V,ct.__COMPILER_RUNTIME={__proto__:null,c:function(R){return V.H.useMemoCache(R)}},ct.cache=function(R){return function(){return R.apply(null,arguments)}},ct.cloneElement=function(R,q,w){if(R==null)throw Error("The argument must be a React element, but you passed "+R+".");var G=A({},R.props),k=R.key,st=void 0;if(q!=null)for(W in q.ref!==void 0&&(st=void 0),q.key!==void 0&&(k=""+q.key),q)!lt.call(q,W)||W==="key"||W==="__self"||W==="__source"||W==="ref"&&q.ref===void 0||(G[W]=q[W]);var W=arguments.length-2;if(W===1)G.children=w;else if(1<W){for(var Zt=Array(W),gt=0;gt<W;gt++)Zt[gt]=arguments[gt+2];G.children=Zt}return et(R.type,k,void 0,void 0,st,G)},ct.createContext=function(R){return R={$$typeof:v,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null},R.Provider=R,R.Consumer={$$typeof:d,_context:R},R},ct.createElement=function(R,q,w){var G,k={},st=null;if(q!=null)for(G in q.key!==void 0&&(st=""+q.key),q)lt.call(q,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&(k[G]=q[G]);var W=arguments.length-2;if(W===1)k.children=w;else if(1<W){for(var Zt=Array(W),gt=0;gt<W;gt++)Zt[gt]=arguments[gt+2];k.children=Zt}if(R&&R.defaultProps)for(G in W=R.defaultProps,W)k[G]===void 0&&(k[G]=W[G]);return et(R,st,void 0,void 0,null,k)},ct.createRef=function(){return{current:null}},ct.forwardRef=function(R){return{$$typeof:p,render:R}},ct.isValidElement=ht,ct.lazy=function(R){return{$$typeof:b,_payload:{_status:-1,_result:R},_init:Y}},ct.memo=function(R,q){return{$$typeof:m,type:R,compare:q===void 0?null:q}},ct.startTransition=function(R){var q=V.T,w={};V.T=w;try{var G=R(),k=V.S;k!==null&&k(w,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(St,P)}catch(st){P(st)}finally{V.T=q}},ct.unstable_useCacheRefresh=function(){return V.H.useCacheRefresh()},ct.use=function(R){return V.H.use(R)},ct.useActionState=function(R,q,w){return V.H.useActionState(R,q,w)},ct.useCallback=function(R,q){return V.H.useCallback(R,q)},ct.useContext=function(R){return V.H.useContext(R)},ct.useDebugValue=function(){},ct.useDeferredValue=function(R,q){return V.H.useDeferredValue(R,q)},ct.useEffect=function(R,q,w){var G=V.H;if(typeof w=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(R,q)},ct.useId=function(){return V.H.useId()},ct.useImperativeHandle=function(R,q,w){return V.H.useImperativeHandle(R,q,w)},ct.useInsertionEffect=function(R,q){return V.H.useInsertionEffect(R,q)},ct.useLayoutEffect=function(R,q){return V.H.useLayoutEffect(R,q)},ct.useMemo=function(R,q){return V.H.useMemo(R,q)},ct.useOptimistic=function(R,q){return V.H.useOptimistic(R,q)},ct.useReducer=function(R,q,w){return V.H.useReducer(R,q,w)},ct.useRef=function(R){return V.H.useRef(R)},ct.useState=function(R){return V.H.useState(R)},ct.useSyncExternalStore=function(R,q,w){return V.H.useSyncExternalStore(R,q,w)},ct.useTransition=function(){return V.H.useTransition()},ct.version="19.1.1",ct}var Uh;function hu(){return Uh||(Uh=1,as.exports=Qv()),as.exports}var ut=hu();const ou=fm(ut);var us={exports:{}},eu={},is={exports:{}},cs={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lh;function Zv(){return Lh||(Lh=1,function(i){function s(C,Y){var P=C.length;C.push(Y);t:for(;0<P;){var St=P-1>>>1,R=C[St];if(0<r(R,Y))C[St]=Y,C[P]=R,P=St;else break t}}function c(C){return C.length===0?null:C[0]}function f(C){if(C.length===0)return null;var Y=C[0],P=C.pop();if(P!==Y){C[0]=P;t:for(var St=0,R=C.length,q=R>>>1;St<q;){var w=2*(St+1)-1,G=C[w],k=w+1,st=C[k];if(0>r(G,P))k<R&&0>r(st,G)?(C[St]=st,C[k]=P,St=k):(C[St]=G,C[w]=P,St=w);else if(k<R&&0>r(st,P))C[St]=st,C[k]=P,St=k;else break t}}return Y}function r(C,Y){var P=C.sortIndex-Y.sortIndex;return P!==0?P:C.id-Y.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;i.unstable_now=function(){return d.now()}}else{var v=Date,p=v.now();i.unstable_now=function(){return v.now()-p}}var y=[],m=[],b=1,g=null,_=3,M=!1,A=!1,U=!1,H=!1,j=typeof setTimeout=="function"?setTimeout:null,at=typeof clearTimeout=="function"?clearTimeout:null,X=typeof setImmediate<"u"?setImmediate:null;function J(C){for(var Y=c(m);Y!==null;){if(Y.callback===null)f(m);else if(Y.startTime<=C)f(m),Y.sortIndex=Y.expirationTime,s(y,Y);else break;Y=c(m)}}function V(C){if(U=!1,J(C),!A)if(c(y)!==null)A=!0,lt||(lt=!0,mt());else{var Y=c(m);Y!==null&&Lt(V,Y.startTime-C)}}var lt=!1,et=-1,nt=5,ht=-1;function Z(){return H?!0:!(i.unstable_now()-ht<nt)}function it(){if(H=!1,lt){var C=i.unstable_now();ht=C;var Y=!0;try{t:{A=!1,U&&(U=!1,at(et),et=-1),M=!0;var P=_;try{e:{for(J(C),g=c(y);g!==null&&!(g.expirationTime>C&&Z());){var St=g.callback;if(typeof St=="function"){g.callback=null,_=g.priorityLevel;var R=St(g.expirationTime<=C);if(C=i.unstable_now(),typeof R=="function"){g.callback=R,J(C),Y=!0;break e}g===c(y)&&f(y),J(C)}else f(y);g=c(y)}if(g!==null)Y=!0;else{var q=c(m);q!==null&&Lt(V,q.startTime-C),Y=!1}}break t}finally{g=null,_=P,M=!1}Y=void 0}}finally{Y?mt():lt=!1}}}var mt;if(typeof X=="function")mt=function(){X(it)};else if(typeof MessageChannel<"u"){var Gt=new MessageChannel,Dt=Gt.port2;Gt.port1.onmessage=it,mt=function(){Dt.postMessage(null)}}else mt=function(){j(it,0)};function Lt(C,Y){et=j(function(){C(i.unstable_now())},Y)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(C){C.callback=null},i.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):nt=0<C?Math.floor(1e3/C):5},i.unstable_getCurrentPriorityLevel=function(){return _},i.unstable_next=function(C){switch(_){case 1:case 2:case 3:var Y=3;break;default:Y=_}var P=_;_=Y;try{return C()}finally{_=P}},i.unstable_requestPaint=function(){H=!0},i.unstable_runWithPriority=function(C,Y){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var P=_;_=C;try{return Y()}finally{_=P}},i.unstable_scheduleCallback=function(C,Y,P){var St=i.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?St+P:St):P=St,C){case 1:var R=-1;break;case 2:R=250;break;case 5:R=1073741823;break;case 4:R=1e4;break;default:R=5e3}return R=P+R,C={id:b++,callback:Y,priorityLevel:C,startTime:P,expirationTime:R,sortIndex:-1},P>St?(C.sortIndex=P,s(m,C),c(y)===null&&C===c(m)&&(U?(at(et),et=-1):U=!0,Lt(V,P-St))):(C.sortIndex=R,s(y,C),A||M||(A=!0,lt||(lt=!0,mt()))),C},i.unstable_shouldYield=Z,i.unstable_wrapCallback=function(C){var Y=_;return function(){var P=_;_=Y;try{return C.apply(this,arguments)}finally{_=P}}}}(cs)),cs}var Nh;function Kv(){return Nh||(Nh=1,is.exports=Zv()),is.exports}var fs={exports:{}},Ft={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bh;function Jv(){if(Bh)return Ft;Bh=1;var i=hu();function s(y){var m="https://react.dev/errors/"+y;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)m+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+y+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var f={d:{f:c,r:function(){throw Error(s(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},r=Symbol.for("react.portal");function d(y,m,b){var g=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:r,key:g==null?null:""+g,children:y,containerInfo:m,implementation:b}}var v=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(y,m){if(y==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return Ft.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=f,Ft.createPortal=function(y,m){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(s(299));return d(y,m,null,b)},Ft.flushSync=function(y){var m=v.T,b=f.p;try{if(v.T=null,f.p=2,y)return y()}finally{v.T=m,f.p=b,f.d.f()}},Ft.preconnect=function(y,m){typeof y=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,f.d.C(y,m))},Ft.prefetchDNS=function(y){typeof y=="string"&&f.d.D(y)},Ft.preinit=function(y,m){if(typeof y=="string"&&m&&typeof m.as=="string"){var b=m.as,g=p(b,m.crossOrigin),_=typeof m.integrity=="string"?m.integrity:void 0,M=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;b==="style"?f.d.S(y,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:g,integrity:_,fetchPriority:M}):b==="script"&&f.d.X(y,{crossOrigin:g,integrity:_,fetchPriority:M,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},Ft.preinitModule=function(y,m){if(typeof y=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var b=p(m.as,m.crossOrigin);f.d.M(y,{crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&f.d.M(y)},Ft.preload=function(y,m){if(typeof y=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var b=m.as,g=p(b,m.crossOrigin);f.d.L(y,b,{crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},Ft.preloadModule=function(y,m){if(typeof y=="string")if(m){var b=p(m.as,m.crossOrigin);f.d.m(y,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else f.d.m(y)},Ft.requestFormReset=function(y){f.d.r(y)},Ft.unstable_batchedUpdates=function(y,m){return y(m)},Ft.useFormState=function(y,m,b){return v.H.useFormState(y,m,b)},Ft.useFormStatus=function(){return v.H.useHostTransitionStatus()},Ft.version="19.1.1",Ft}var Hh;function sm(){if(Hh)return fs.exports;Hh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(s){console.error(s)}}return i(),fs.exports=Jv(),fs.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qh;function $v(){if(qh)return eu;qh=1;var i=Kv(),s=hu(),c=sm();function f(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function v(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(d(t)!==t)throw Error(f(188))}function y(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(f(188));return e!==t?null:t}for(var n=t,l=e;;){var a=n.return;if(a===null)break;var u=a.alternate;if(u===null){if(l=a.return,l!==null){n=l;continue}break}if(a.child===u.child){for(u=a.child;u;){if(u===n)return p(a),t;if(u===l)return p(a),e;u=u.sibling}throw Error(f(188))}if(n.return!==l.return)n=a,l=u;else{for(var o=!1,h=a.child;h;){if(h===n){o=!0,n=a,l=u;break}if(h===l){o=!0,l=a,n=u;break}h=h.sibling}if(!o){for(h=u.child;h;){if(h===n){o=!0,n=u,l=a;break}if(h===l){o=!0,l=u,n=a;break}h=h.sibling}if(!o)throw Error(f(189))}}if(n.alternate!==l)throw Error(f(190))}if(n.tag!==3)throw Error(f(188));return n.stateNode.current===n?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var b=Object.assign,g=Symbol.for("react.element"),_=Symbol.for("react.transitional.element"),M=Symbol.for("react.portal"),A=Symbol.for("react.fragment"),U=Symbol.for("react.strict_mode"),H=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),at=Symbol.for("react.consumer"),X=Symbol.for("react.context"),J=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),lt=Symbol.for("react.suspense_list"),et=Symbol.for("react.memo"),nt=Symbol.for("react.lazy"),ht=Symbol.for("react.activity"),Z=Symbol.for("react.memo_cache_sentinel"),it=Symbol.iterator;function mt(t){return t===null||typeof t!="object"?null:(t=it&&t[it]||t["@@iterator"],typeof t=="function"?t:null)}var Gt=Symbol.for("react.client.reference");function Dt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Gt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case A:return"Fragment";case H:return"Profiler";case U:return"StrictMode";case V:return"Suspense";case lt:return"SuspenseList";case ht:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case M:return"Portal";case X:return(t.displayName||"Context")+".Provider";case at:return(t._context.displayName||"Context")+".Consumer";case J:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case et:return e=t.displayName||null,e!==null?e:Dt(t.type)||"Memo";case nt:e=t._payload,t=t._init;try{return Dt(t(e))}catch{}}return null}var Lt=Array.isArray,C=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},St=[],R=-1;function q(t){return{current:t}}function w(t){0>R||(t.current=St[R],St[R]=null,R--)}function G(t,e){R++,St[R]=t.current,t.current=e}var k=q(null),st=q(null),W=q(null),Zt=q(null);function gt(t,e){switch(G(W,e),G(st,t),G(k,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?lh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=lh(e),t=ah(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}w(k),G(k,t)}function ye(){w(k),w(st),w(W)}function Nn(t){t.memoizedState!==null&&G(Zt,t);var e=k.current,n=ah(e,t.type);e!==n&&(G(st,t),G(k,n))}function ve(t){st.current===t&&(w(k),w(st)),Zt.current===t&&(w(Zt),ka._currentValue=P)}var ta=Object.prototype.hasOwnProperty,ea=i.unstable_scheduleCallback,ul=i.unstable_cancelCallback,Xi=i.unstable_shouldYield,Qi=i.unstable_requestPaint,ge=i.unstable_now,il=i.unstable_getCurrentPriorityLevel,Bn=i.unstable_ImmediatePriority,na=i.unstable_UserBlockingPriority,Hn=i.unstable_NormalPriority,Ot=i.unstable_LowPriority,Kt=i.unstable_IdlePriority,Ne=i.log,js=i.unstable_setDisableYieldValue,la=null,ie=null;function cn(t){if(typeof Ne=="function"&&js(t),ie&&typeof ie.setStrictMode=="function")try{ie.setStrictMode(la,t)}catch{}}var ce=Math.clz32?Math.clz32:zm,Am=Math.log,Om=Math.LN2;function zm(t){return t>>>=0,t===0?32:31-(Am(t)/Om|0)|0}var mu=256,yu=4194304;function qn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function vu(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var a=0,u=t.suspendedLanes,o=t.pingedLanes;t=t.warmLanes;var h=l&134217727;return h!==0?(l=h&~u,l!==0?a=qn(l):(o&=h,o!==0?a=qn(o):n||(n=h&~t,n!==0&&(a=qn(n))))):(h=l&~u,h!==0?a=qn(h):o!==0?a=qn(o):n||(n=l&~t,n!==0&&(a=qn(n)))),a===0?0:e!==0&&e!==a&&!(e&u)&&(u=a&-a,n=e&-e,u>=n||u===32&&(n&4194048)!==0)?e:a}function aa(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Dm(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ys(){var t=mu;return mu<<=1,!(mu&4194048)&&(mu=256),t}function Gs(){var t=yu;return yu<<=1,!(yu&62914560)&&(yu=4194304),t}function Zi(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ua(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function xm(t,e,n,l,a,u){var o=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var h=t.entanglements,S=t.expirationTimes,z=t.hiddenUpdates;for(n=o&~n;0<n;){var L=31-ce(n),B=1<<L;h[L]=0,S[L]=-1;var D=z[L];if(D!==null)for(z[L]=null,L=0;L<D.length;L++){var x=D[L];x!==null&&(x.lane&=-536870913)}n&=~B}l!==0&&Vs(t,l,0),u!==0&&a===0&&t.tag!==0&&(t.suspendedLanes|=u&~(o&~e))}function Vs(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ce(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function ws(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ce(n),a=1<<l;a&e|t[l]&e&&(t[l]|=e),n&=~a}}function Ki(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Ji(t){return t&=-t,2<t?8<t?t&134217727?32:268435456:8:2}function Xs(){var t=Y.p;return t!==0?t:(t=window.event,t===void 0?32:Eh(t.type))}function Cm(t,e){var n=Y.p;try{return Y.p=t,e()}finally{Y.p=n}}var fn=Math.random().toString(36).slice(2),Pt="__reactFiber$"+fn,te="__reactProps$"+fn,cl="__reactContainer$"+fn,$i="__reactEvents$"+fn,Um="__reactListeners$"+fn,Lm="__reactHandles$"+fn,Qs="__reactResources$"+fn,ia="__reactMarker$"+fn;function ki(t){delete t[Pt],delete t[te],delete t[$i],delete t[Um],delete t[Lm]}function fl(t){var e=t[Pt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[cl]||n[Pt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=fh(t);t!==null;){if(n=t[Pt])return n;t=fh(t)}return e}t=n,n=t.parentNode}return null}function sl(t){if(t=t[Pt]||t[cl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function ca(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(f(33))}function ol(t){var e=t[Qs];return e||(e=t[Qs]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Vt(t){t[ia]=!0}var Zs=new Set,Ks={};function jn(t,e){rl(t,e),rl(t+"Capture",e)}function rl(t,e){for(Ks[t]=e,t=0;t<e.length;t++)Zs.add(e[t])}var Nm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Js={},$s={};function Bm(t){return ta.call($s,t)?!0:ta.call(Js,t)?!1:Nm.test(t)?$s[t]=!0:(Js[t]=!0,!1)}function gu(t,e,n){if(Bm(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function pu(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function we(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var Pi,ks;function dl(t){if(Pi===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Pi=e&&e[1]||"",ks=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Pi+t+ks}var Wi=!1;function Fi(t,e){if(!t||Wi)return"";Wi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var B=function(){throw Error()};if(Object.defineProperty(B.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(B,[])}catch(x){var D=x}Reflect.construct(t,[],B)}else{try{B.call()}catch(x){D=x}t.call(B.prototype)}}else{try{throw Error()}catch(x){D=x}(B=t())&&typeof B.catch=="function"&&B.catch(function(){})}}catch(x){if(x&&D&&typeof x.stack=="string")return[x.stack,D.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),o=u[0],h=u[1];if(o&&h){var S=o.split(`
`),z=h.split(`
`);for(a=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;a<z.length&&!z[a].includes("DetermineComponentFrameRoot");)a++;if(l===S.length||a===z.length)for(l=S.length-1,a=z.length-1;1<=l&&0<=a&&S[l]!==z[a];)a--;for(;1<=l&&0<=a;l--,a--)if(S[l]!==z[a]){if(l!==1||a!==1)do if(l--,a--,0>a||S[l]!==z[a]){var L=`
`+S[l].replace(" at new "," at ");return t.displayName&&L.includes("<anonymous>")&&(L=L.replace("<anonymous>",t.displayName)),L}while(1<=l&&0<=a);break}}}finally{Wi=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?dl(n):""}function Hm(t){switch(t.tag){case 26:case 27:case 5:return dl(t.type);case 16:return dl("Lazy");case 13:return dl("Suspense");case 19:return dl("SuspenseList");case 0:case 15:return Fi(t.type,!1);case 11:return Fi(t.type.render,!1);case 1:return Fi(t.type,!0);case 31:return dl("Activity");default:return""}}function Ps(t){try{var e="";do e+=Hm(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function pe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ws(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function qm(t){var e=Ws(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var a=n.get,u=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return a.call(this)},set:function(o){l=""+o,u.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(o){l=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Su(t){t._valueTracker||(t._valueTracker=qm(t))}function Fs(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Ws(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function _u(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var jm=/[\n"\\]/g;function Se(t){return t.replace(jm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ii(t,e,n,l,a,u,o,h){t.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.type=o:t.removeAttribute("type"),e!=null?o==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+pe(e)):t.value!==""+pe(e)&&(t.value=""+pe(e)):o!=="submit"&&o!=="reset"||t.removeAttribute("value"),e!=null?tc(t,o,pe(e)):n!=null?tc(t,o,pe(n)):l!=null&&t.removeAttribute("value"),a==null&&u!=null&&(t.defaultChecked=!!u),a!=null&&(t.checked=a&&typeof a!="function"&&typeof a!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.name=""+pe(h):t.removeAttribute("name")}function Is(t,e,n,l,a,u,o,h){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||n!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;n=n!=null?""+pe(n):"",e=e!=null?""+pe(e):n,h||e===t.value||(t.value=e),t.defaultValue=e}l=l??a,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=h?t.checked:!!l,t.defaultChecked=!!l,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.name=o)}function tc(t,e,n){e==="number"&&_u(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function hl(t,e,n,l){if(t=t.options,e){e={};for(var a=0;a<n.length;a++)e["$"+n[a]]=!0;for(n=0;n<t.length;n++)a=e.hasOwnProperty("$"+t[n].value),t[n].selected!==a&&(t[n].selected=a),a&&l&&(t[n].defaultSelected=!0)}else{for(n=""+pe(n),e=null,a=0;a<t.length;a++){if(t[a].value===n){t[a].selected=!0,l&&(t[a].defaultSelected=!0);return}e!==null||t[a].disabled||(e=t[a])}e!==null&&(e.selected=!0)}}function to(t,e,n){if(e!=null&&(e=""+pe(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+pe(n):""}function eo(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(f(92));if(Lt(l)){if(1<l.length)throw Error(f(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=pe(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function ml(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Ym=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function no(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||Ym.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function lo(t,e,n){if(e!=null&&typeof e!="object")throw Error(f(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var a in e)l=e[a],e.hasOwnProperty(a)&&n[a]!==l&&no(t,a,l)}else for(var u in e)e.hasOwnProperty(u)&&no(t,u,e[u])}function ec(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Gm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Vm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function bu(t){return Vm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var nc=null;function lc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var yl=null,vl=null;function ao(t){var e=sl(t);if(e&&(t=e.stateNode)){var n=t[te]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ii(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Se(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var a=l[te]||null;if(!a)throw Error(f(90));Ii(l,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Fs(l)}break t;case"textarea":to(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&hl(t,!!n.multiple,e,!1)}}}var ac=!1;function uo(t,e,n){if(ac)return t(e,n);ac=!0;try{var l=t(e);return l}finally{if(ac=!1,(yl!==null||vl!==null)&&(ii(),yl&&(e=yl,t=vl,vl=yl=null,ao(e),t)))for(e=0;e<t.length;e++)ao(t[e])}}function fa(t,e){var n=t.stateNode;if(n===null)return null;var l=n[te]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(f(231,e,typeof n));return n}var Xe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),uc=!1;if(Xe)try{var sa={};Object.defineProperty(sa,"passive",{get:function(){uc=!0}}),window.addEventListener("test",sa,sa),window.removeEventListener("test",sa,sa)}catch{uc=!1}var sn=null,ic=null,Ru=null;function io(){if(Ru)return Ru;var t,e=ic,n=e.length,l,a="value"in sn?sn.value:sn.textContent,u=a.length;for(t=0;t<n&&e[t]===a[t];t++);var o=n-t;for(l=1;l<=o&&e[n-l]===a[u-l];l++);return Ru=a.slice(t,1<l?1-l:void 0)}function Eu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Tu(){return!0}function co(){return!1}function ee(t){function e(n,l,a,u,o){this._reactName=n,this._targetInst=a,this.type=l,this.nativeEvent=u,this.target=o,this.currentTarget=null;for(var h in t)t.hasOwnProperty(h)&&(n=t[h],this[h]=n?n(u):u[h]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Tu:co,this.isPropagationStopped=co,this}return b(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Tu)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Tu)},persist:function(){},isPersistent:Tu}),e}var Yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Mu=ee(Yn),oa=b({},Yn,{view:0,detail:0}),wm=ee(oa),cc,fc,ra,Au=b({},oa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:oc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ra&&(ra&&t.type==="mousemove"?(cc=t.screenX-ra.screenX,fc=t.screenY-ra.screenY):fc=cc=0,ra=t),cc)},movementY:function(t){return"movementY"in t?t.movementY:fc}}),fo=ee(Au),Xm=b({},Au,{dataTransfer:0}),Qm=ee(Xm),Zm=b({},oa,{relatedTarget:0}),sc=ee(Zm),Km=b({},Yn,{animationName:0,elapsedTime:0,pseudoElement:0}),Jm=ee(Km),$m=b({},Yn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),km=ee($m),Pm=b({},Yn,{data:0}),so=ee(Pm),Wm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Im={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ty(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Im[t])?!!e[t]:!1}function oc(){return ty}var ey=b({},oa,{key:function(t){if(t.key){var e=Wm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Eu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Fm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:oc,charCode:function(t){return t.type==="keypress"?Eu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Eu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),ny=ee(ey),ly=b({},Au,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),oo=ee(ly),ay=b({},oa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:oc}),uy=ee(ay),iy=b({},Yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),cy=ee(iy),fy=b({},Au,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),sy=ee(fy),oy=b({},Yn,{newState:0,oldState:0}),ry=ee(oy),dy=[9,13,27,32],rc=Xe&&"CompositionEvent"in window,da=null;Xe&&"documentMode"in document&&(da=document.documentMode);var hy=Xe&&"TextEvent"in window&&!da,ro=Xe&&(!rc||da&&8<da&&11>=da),ho=" ",mo=!1;function yo(t,e){switch(t){case"keyup":return dy.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var gl=!1;function my(t,e){switch(t){case"compositionend":return vo(e);case"keypress":return e.which!==32?null:(mo=!0,ho);case"textInput":return t=e.data,t===ho&&mo?null:t;default:return null}}function yy(t,e){if(gl)return t==="compositionend"||!rc&&yo(t,e)?(t=io(),Ru=ic=sn=null,gl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return ro&&e.locale!=="ko"?null:e.data;default:return null}}var vy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function go(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!vy[t.type]:e==="textarea"}function po(t,e,n,l){yl?vl?vl.push(l):vl=[l]:yl=l,e=di(e,"onChange"),0<e.length&&(n=new Mu("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var ha=null,ma=null;function gy(t){Fd(t,0)}function Ou(t){var e=ca(t);if(Fs(e))return t}function So(t,e){if(t==="change")return e}var _o=!1;if(Xe){var dc;if(Xe){var hc="oninput"in document;if(!hc){var bo=document.createElement("div");bo.setAttribute("oninput","return;"),hc=typeof bo.oninput=="function"}dc=hc}else dc=!1;_o=dc&&(!document.documentMode||9<document.documentMode)}function Ro(){ha&&(ha.detachEvent("onpropertychange",Eo),ma=ha=null)}function Eo(t){if(t.propertyName==="value"&&Ou(ma)){var e=[];po(e,ma,t,lc(t)),uo(gy,e)}}function py(t,e,n){t==="focusin"?(Ro(),ha=e,ma=n,ha.attachEvent("onpropertychange",Eo)):t==="focusout"&&Ro()}function Sy(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Ou(ma)}function _y(t,e){if(t==="click")return Ou(e)}function by(t,e){if(t==="input"||t==="change")return Ou(e)}function Ry(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var fe=typeof Object.is=="function"?Object.is:Ry;function ya(t,e){if(fe(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var a=n[l];if(!ta.call(e,a)||!fe(t[a],e[a]))return!1}return!0}function To(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Mo(t,e){var n=To(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=To(n)}}function Ao(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Ao(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Oo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=_u(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=_u(t.document)}return e}function mc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Ey=Xe&&"documentMode"in document&&11>=document.documentMode,pl=null,yc=null,va=null,vc=!1;function zo(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;vc||pl==null||pl!==_u(l)||(l=pl,"selectionStart"in l&&mc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),va&&ya(va,l)||(va=l,l=di(yc,"onSelect"),0<l.length&&(e=new Mu("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=pl)))}function Gn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Sl={animationend:Gn("Animation","AnimationEnd"),animationiteration:Gn("Animation","AnimationIteration"),animationstart:Gn("Animation","AnimationStart"),transitionrun:Gn("Transition","TransitionRun"),transitionstart:Gn("Transition","TransitionStart"),transitioncancel:Gn("Transition","TransitionCancel"),transitionend:Gn("Transition","TransitionEnd")},gc={},Do={};Xe&&(Do=document.createElement("div").style,"AnimationEvent"in window||(delete Sl.animationend.animation,delete Sl.animationiteration.animation,delete Sl.animationstart.animation),"TransitionEvent"in window||delete Sl.transitionend.transition);function Vn(t){if(gc[t])return gc[t];if(!Sl[t])return t;var e=Sl[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Do)return gc[t]=e[n];return t}var xo=Vn("animationend"),Co=Vn("animationiteration"),Uo=Vn("animationstart"),Ty=Vn("transitionrun"),My=Vn("transitionstart"),Ay=Vn("transitioncancel"),Lo=Vn("transitionend"),No=new Map,pc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");pc.push("scrollEnd");function Ce(t,e){No.set(t,e),jn(e,[t])}var Bo=new WeakMap;function _e(t,e){if(typeof t=="object"&&t!==null){var n=Bo.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Ps(e)},Bo.set(t,e),e)}return{value:t,source:e,stack:Ps(e)}}var be=[],_l=0,Sc=0;function zu(){for(var t=_l,e=Sc=_l=0;e<t;){var n=be[e];be[e++]=null;var l=be[e];be[e++]=null;var a=be[e];be[e++]=null;var u=be[e];if(be[e++]=null,l!==null&&a!==null){var o=l.pending;o===null?a.next=a:(a.next=o.next,o.next=a),l.pending=a}u!==0&&Ho(n,a,u)}}function Du(t,e,n,l){be[_l++]=t,be[_l++]=e,be[_l++]=n,be[_l++]=l,Sc|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function _c(t,e,n,l){return Du(t,e,n,l),xu(t)}function bl(t,e){return Du(t,null,null,e),xu(t)}function Ho(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var a=!1,u=t.return;u!==null;)u.childLanes|=n,l=u.alternate,l!==null&&(l.childLanes|=n),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(a=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,a&&e!==null&&(a=31-ce(n),t=u.hiddenUpdates,l=t[a],l===null?t[a]=[e]:l.push(e),e.lane=n|536870912),u):null}function xu(t){if(50<Va)throw Va=0,Of=null,Error(f(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Rl={};function Oy(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function se(t,e,n,l){return new Oy(t,e,n,l)}function bc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Qe(t,e){var n=t.alternate;return n===null?(n=se(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function qo(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Cu(t,e,n,l,a,u){var o=0;if(l=t,typeof t=="function")bc(t)&&(o=1);else if(typeof t=="string")o=Dv(t,n,k.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case ht:return t=se(31,n,e,a),t.elementType=ht,t.lanes=u,t;case A:return wn(n.children,a,u,e);case U:o=8,a|=24;break;case H:return t=se(12,n,e,a|2),t.elementType=H,t.lanes=u,t;case V:return t=se(13,n,e,a),t.elementType=V,t.lanes=u,t;case lt:return t=se(19,n,e,a),t.elementType=lt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case j:case X:o=10;break t;case at:o=9;break t;case J:o=11;break t;case et:o=14;break t;case nt:o=16,l=null;break t}o=29,n=Error(f(130,t===null?"null":typeof t,"")),l=null}return e=se(o,n,e,a),e.elementType=t,e.type=l,e.lanes=u,e}function wn(t,e,n,l){return t=se(7,t,l,e),t.lanes=n,t}function Rc(t,e,n){return t=se(6,t,null,e),t.lanes=n,t}function Ec(t,e,n){return e=se(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var El=[],Tl=0,Uu=null,Lu=0,Re=[],Ee=0,Xn=null,Ze=1,Ke="";function Qn(t,e){El[Tl++]=Lu,El[Tl++]=Uu,Uu=t,Lu=e}function jo(t,e,n){Re[Ee++]=Ze,Re[Ee++]=Ke,Re[Ee++]=Xn,Xn=t;var l=Ze;t=Ke;var a=32-ce(l)-1;l&=~(1<<a),n+=1;var u=32-ce(e)+a;if(30<u){var o=a-a%5;u=(l&(1<<o)-1).toString(32),l>>=o,a-=o,Ze=1<<32-ce(e)+a|n<<a|l,Ke=u+t}else Ze=1<<u|n<<a|l,Ke=t}function Tc(t){t.return!==null&&(Qn(t,1),jo(t,1,0))}function Mc(t){for(;t===Uu;)Uu=El[--Tl],El[Tl]=null,Lu=El[--Tl],El[Tl]=null;for(;t===Xn;)Xn=Re[--Ee],Re[Ee]=null,Ke=Re[--Ee],Re[Ee]=null,Ze=Re[--Ee],Re[Ee]=null}var It=null,Ct=null,pt=!1,Zn=null,Be=!1,Ac=Error(f(519));function Kn(t){var e=Error(f(418,""));throw Sa(_e(e,t)),Ac}function Yo(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Pt]=t,e[te]=l,n){case"dialog":dt("cancel",e),dt("close",e);break;case"iframe":case"object":case"embed":dt("load",e);break;case"video":case"audio":for(n=0;n<Xa.length;n++)dt(Xa[n],e);break;case"source":dt("error",e);break;case"img":case"image":case"link":dt("error",e),dt("load",e);break;case"details":dt("toggle",e);break;case"input":dt("invalid",e),Is(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Su(e);break;case"select":dt("invalid",e);break;case"textarea":dt("invalid",e),eo(e,l.value,l.defaultValue,l.children),Su(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||nh(e.textContent,n)?(l.popover!=null&&(dt("beforetoggle",e),dt("toggle",e)),l.onScroll!=null&&dt("scroll",e),l.onScrollEnd!=null&&dt("scrollend",e),l.onClick!=null&&(e.onclick=hi),e=!0):e=!1,e||Kn(t)}function Go(t){for(It=t.return;It;)switch(It.tag){case 5:case 13:Be=!1;return;case 27:case 3:Be=!0;return;default:It=It.return}}function ga(t){if(t!==It)return!1;if(!pt)return Go(t),pt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||Xf(t.type,t.memoizedProps)),n=!n),n&&Ct&&Kn(t),Go(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(f(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Ct=Le(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Ct=null}}else e===27?(e=Ct,Mn(t.type)?(t=Jf,Jf=null,Ct=t):Ct=e):Ct=It?Le(t.stateNode.nextSibling):null;return!0}function pa(){Ct=It=null,pt=!1}function Vo(){var t=Zn;return t!==null&&(ae===null?ae=t:ae.push.apply(ae,t),Zn=null),t}function Sa(t){Zn===null?Zn=[t]:Zn.push(t)}var Oc=q(null),Jn=null,Je=null;function on(t,e,n){G(Oc,e._currentValue),e._currentValue=n}function $e(t){t._currentValue=Oc.current,w(Oc)}function zc(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function Dc(t,e,n,l){var a=t.child;for(a!==null&&(a.return=t);a!==null;){var u=a.dependencies;if(u!==null){var o=a.child;u=u.firstContext;t:for(;u!==null;){var h=u;u=a;for(var S=0;S<e.length;S++)if(h.context===e[S]){u.lanes|=n,h=u.alternate,h!==null&&(h.lanes|=n),zc(u.return,n,t),l||(o=null);break t}u=h.next}}else if(a.tag===18){if(o=a.return,o===null)throw Error(f(341));o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),zc(o,n,t),o=null}else o=a.child;if(o!==null)o.return=a;else for(o=a;o!==null;){if(o===t){o=null;break}if(a=o.sibling,a!==null){a.return=o.return,o=a;break}o=o.return}a=o}}function _a(t,e,n,l){t=null;for(var a=e,u=!1;a!==null;){if(!u){if(a.flags&524288)u=!0;else if(a.flags&262144)break}if(a.tag===10){var o=a.alternate;if(o===null)throw Error(f(387));if(o=o.memoizedProps,o!==null){var h=a.type;fe(a.pendingProps.value,o.value)||(t!==null?t.push(h):t=[h])}}else if(a===Zt.current){if(o=a.alternate,o===null)throw Error(f(387));o.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(t!==null?t.push(ka):t=[ka])}a=a.return}t!==null&&Dc(e,t,n,l),e.flags|=262144}function Nu(t){for(t=t.firstContext;t!==null;){if(!fe(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function $n(t){Jn=t,Je=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Wt(t){return wo(Jn,t)}function Bu(t,e){return Jn===null&&$n(t),wo(t,e)}function wo(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},Je===null){if(t===null)throw Error(f(308));Je=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Je=Je.next=e;return n}var zy=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Dy=i.unstable_scheduleCallback,xy=i.unstable_NormalPriority,jt={$$typeof:X,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function xc(){return{controller:new zy,data:new Map,refCount:0}}function ba(t){t.refCount--,t.refCount===0&&Dy(xy,function(){t.controller.abort()})}var Ra=null,Cc=0,Ml=0,Al=null;function Cy(t,e){if(Ra===null){var n=Ra=[];Cc=0,Ml=Nf(),Al={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Cc++,e.then(Xo,Xo),e}function Xo(){if(--Cc===0&&Ra!==null){Al!==null&&(Al.status="fulfilled");var t=Ra;Ra=null,Ml=0,Al=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Uy(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(a){n.push(a)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var a=0;a<n.length;a++)(0,n[a])(e)},function(a){for(l.status="rejected",l.reason=a,a=0;a<n.length;a++)(0,n[a])(void 0)}),l}var Qo=C.S;C.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Cy(t,e),Qo!==null&&Qo(t,e)};var kn=q(null);function Uc(){var t=kn.current;return t!==null?t:At.pooledCache}function Hu(t,e){e===null?G(kn,kn.current):G(kn,e.pool)}function Zo(){var t=Uc();return t===null?null:{parent:jt._currentValue,pool:t}}var Ea=Error(f(460)),Ko=Error(f(474)),qu=Error(f(542)),Lc={then:function(){}};function Jo(t){return t=t.status,t==="fulfilled"||t==="rejected"}function ju(){}function $o(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(ju,ju),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Po(t),t;default:if(typeof e.status=="string")e.then(ju,ju);else{if(t=At,t!==null&&100<t.shellSuspendCounter)throw Error(f(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var a=e;a.status="fulfilled",a.value=l}},function(l){if(e.status==="pending"){var a=e;a.status="rejected",a.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Po(t),t}throw Ta=e,Ea}}var Ta=null;function ko(){if(Ta===null)throw Error(f(459));var t=Ta;return Ta=null,t}function Po(t){if(t===Ea||t===qu)throw Error(f(483))}var rn=!1;function Nc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Bc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function dn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function hn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,_t&2){var a=l.pending;return a===null?e.next=e:(e.next=a.next,a.next=e),l.pending=e,e=xu(t),Ho(t,null,n),e}return Du(t,l,e,n),xu(t)}function Ma(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,ws(t,n)}}function Hc(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var a=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};u===null?a=u=o:u=u.next=o,n=n.next}while(n!==null);u===null?a=u=e:u=u.next=e}else a=u=e;n={baseState:l.baseState,firstBaseUpdate:a,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var qc=!1;function Aa(){if(qc){var t=Al;if(t!==null)throw t}}function Oa(t,e,n,l){qc=!1;var a=t.updateQueue;rn=!1;var u=a.firstBaseUpdate,o=a.lastBaseUpdate,h=a.shared.pending;if(h!==null){a.shared.pending=null;var S=h,z=S.next;S.next=null,o===null?u=z:o.next=z,o=S;var L=t.alternate;L!==null&&(L=L.updateQueue,h=L.lastBaseUpdate,h!==o&&(h===null?L.firstBaseUpdate=z:h.next=z,L.lastBaseUpdate=S))}if(u!==null){var B=a.baseState;o=0,L=z=S=null,h=u;do{var D=h.lane&-536870913,x=D!==h.lane;if(x?(yt&D)===D:(l&D)===D){D!==0&&D===Ml&&(qc=!0),L!==null&&(L=L.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});t:{var tt=t,F=h;D=e;var Tt=n;switch(F.tag){case 1:if(tt=F.payload,typeof tt=="function"){B=tt.call(Tt,B,D);break t}B=tt;break t;case 3:tt.flags=tt.flags&-65537|128;case 0:if(tt=F.payload,D=typeof tt=="function"?tt.call(Tt,B,D):tt,D==null)break t;B=b({},B,D);break t;case 2:rn=!0}}D=h.callback,D!==null&&(t.flags|=64,x&&(t.flags|=8192),x=a.callbacks,x===null?a.callbacks=[D]:x.push(D))}else x={lane:D,tag:h.tag,payload:h.payload,callback:h.callback,next:null},L===null?(z=L=x,S=B):L=L.next=x,o|=D;if(h=h.next,h===null){if(h=a.shared.pending,h===null)break;x=h,h=x.next,x.next=null,a.lastBaseUpdate=x,a.shared.pending=null}}while(!0);L===null&&(S=B),a.baseState=S,a.firstBaseUpdate=z,a.lastBaseUpdate=L,u===null&&(a.shared.lanes=0),bn|=o,t.lanes=o,t.memoizedState=B}}function Wo(t,e){if(typeof t!="function")throw Error(f(191,t));t.call(e)}function Fo(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Wo(n[t],e)}var Ol=q(null),Yu=q(0);function Io(t,e){t=en,G(Yu,t),G(Ol,e),en=t|e.baseLanes}function jc(){G(Yu,en),G(Ol,Ol.current)}function Yc(){en=Yu.current,w(Ol),w(Yu)}var mn=0,ft=null,Rt=null,Ht=null,Gu=!1,zl=!1,Pn=!1,Vu=0,za=0,Dl=null,Ly=0;function Nt(){throw Error(f(321))}function Gc(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!fe(t[n],e[n]))return!1;return!0}function Vc(t,e,n,l,a,u){return mn=u,ft=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,C.H=t===null||t.memoizedState===null?Hr:qr,Pn=!1,u=n(l,a),Pn=!1,zl&&(u=er(e,n,l,a)),tr(t),u}function tr(t){C.H=Ju;var e=Rt!==null&&Rt.next!==null;if(mn=0,Ht=Rt=ft=null,Gu=!1,za=0,Dl=null,e)throw Error(f(300));t===null||wt||(t=t.dependencies,t!==null&&Nu(t)&&(wt=!0))}function er(t,e,n,l){ft=t;var a=0;do{if(zl&&(Dl=null),za=0,zl=!1,25<=a)throw Error(f(301));if(a+=1,Ht=Rt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}C.H=Gy,u=e(n,l)}while(zl);return u}function Ny(){var t=C.H,e=t.useState()[0];return e=typeof e.then=="function"?Da(e):e,t=t.useState()[0],(Rt!==null?Rt.memoizedState:null)!==t&&(ft.flags|=1024),e}function wc(){var t=Vu!==0;return Vu=0,t}function Xc(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Qc(t){if(Gu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Gu=!1}mn=0,Ht=Rt=ft=null,zl=!1,za=Vu=0,Dl=null}function ne(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ht===null?ft.memoizedState=Ht=t:Ht=Ht.next=t,Ht}function qt(){if(Rt===null){var t=ft.alternate;t=t!==null?t.memoizedState:null}else t=Rt.next;var e=Ht===null?ft.memoizedState:Ht.next;if(e!==null)Ht=e,Rt=t;else{if(t===null)throw ft.alternate===null?Error(f(467)):Error(f(310));Rt=t,t={memoizedState:Rt.memoizedState,baseState:Rt.baseState,baseQueue:Rt.baseQueue,queue:Rt.queue,next:null},Ht===null?ft.memoizedState=Ht=t:Ht=Ht.next=t}return Ht}function Zc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Da(t){var e=za;return za+=1,Dl===null&&(Dl=[]),t=$o(Dl,t,e),e=ft,(Ht===null?e.memoizedState:Ht.next)===null&&(e=e.alternate,C.H=e===null||e.memoizedState===null?Hr:qr),t}function wu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Da(t);if(t.$$typeof===X)return Wt(t)}throw Error(f(438,String(t)))}function Kc(t){var e=null,n=ft.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=ft.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(a){return a.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=Zc(),ft.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=Z;return e.index++,n}function ke(t,e){return typeof e=="function"?e(t):e}function Xu(t){var e=qt();return Jc(e,Rt,t)}function Jc(t,e,n){var l=t.queue;if(l===null)throw Error(f(311));l.lastRenderedReducer=n;var a=t.baseQueue,u=l.pending;if(u!==null){if(a!==null){var o=a.next;a.next=u.next,u.next=o}e.baseQueue=a=u,l.pending=null}if(u=t.baseState,a===null)t.memoizedState=u;else{e=a.next;var h=o=null,S=null,z=e,L=!1;do{var B=z.lane&-536870913;if(B!==z.lane?(yt&B)===B:(mn&B)===B){var D=z.revertLane;if(D===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),B===Ml&&(L=!0);else if((mn&D)===D){z=z.next,D===Ml&&(L=!0);continue}else B={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},S===null?(h=S=B,o=u):S=S.next=B,ft.lanes|=D,bn|=D;B=z.action,Pn&&n(u,B),u=z.hasEagerState?z.eagerState:n(u,B)}else D={lane:B,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},S===null?(h=S=D,o=u):S=S.next=D,ft.lanes|=B,bn|=B;z=z.next}while(z!==null&&z!==e);if(S===null?o=u:S.next=h,!fe(u,t.memoizedState)&&(wt=!0,L&&(n=Al,n!==null)))throw n;t.memoizedState=u,t.baseState=o,t.baseQueue=S,l.lastRenderedState=u}return a===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function $c(t){var e=qt(),n=e.queue;if(n===null)throw Error(f(311));n.lastRenderedReducer=t;var l=n.dispatch,a=n.pending,u=e.memoizedState;if(a!==null){n.pending=null;var o=a=a.next;do u=t(u,o.action),o=o.next;while(o!==a);fe(u,e.memoizedState)||(wt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),n.lastRenderedState=u}return[u,l]}function nr(t,e,n){var l=ft,a=qt(),u=pt;if(u){if(n===void 0)throw Error(f(407));n=n()}else n=e();var o=!fe((Rt||a).memoizedState,n);o&&(a.memoizedState=n,wt=!0),a=a.queue;var h=ur.bind(null,l,a,t);if(xa(2048,8,h,[t]),a.getSnapshot!==e||o||Ht!==null&&Ht.memoizedState.tag&1){if(l.flags|=2048,xl(9,Qu(),ar.bind(null,l,a,n,e),null),At===null)throw Error(f(349));u||mn&124||lr(l,e,n)}return n}function lr(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ft.updateQueue,e===null?(e=Zc(),ft.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function ar(t,e,n,l){e.value=n,e.getSnapshot=l,ir(e)&&cr(t)}function ur(t,e,n){return n(function(){ir(e)&&cr(t)})}function ir(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!fe(t,n)}catch{return!0}}function cr(t){var e=bl(t,2);e!==null&&me(e,t,2)}function kc(t){var e=ne();if(typeof t=="function"){var n=t;if(t=n(),Pn){cn(!0);try{n()}finally{cn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:t},e}function fr(t,e,n,l){return t.baseState=n,Jc(t,Rt,typeof l=="function"?l:ke)}function By(t,e,n,l,a){if(Ku(t))throw Error(f(485));if(t=e.action,t!==null){var u={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){u.listeners.push(o)}};C.T!==null?n(!0):u.isTransition=!1,l(u),n=e.pending,n===null?(u.next=e.pending=u,sr(e,u)):(u.next=n.next,e.pending=n.next=u)}}function sr(t,e){var n=e.action,l=e.payload,a=t.state;if(e.isTransition){var u=C.T,o={};C.T=o;try{var h=n(a,l),S=C.S;S!==null&&S(o,h),or(t,e,h)}catch(z){Pc(t,e,z)}finally{C.T=u}}else try{u=n(a,l),or(t,e,u)}catch(z){Pc(t,e,z)}}function or(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){rr(t,e,l)},function(l){return Pc(t,e,l)}):rr(t,e,n)}function rr(t,e,n){e.status="fulfilled",e.value=n,dr(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,sr(t,n)))}function Pc(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,dr(e),e=e.next;while(e!==l)}t.action=null}function dr(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function hr(t,e){return e}function mr(t,e){if(pt){var n=At.formState;if(n!==null){t:{var l=ft;if(pt){if(Ct){e:{for(var a=Ct,u=Be;a.nodeType!==8;){if(!u){a=null;break e}if(a=Le(a.nextSibling),a===null){a=null;break e}}u=a.data,a=u==="F!"||u==="F"?a:null}if(a){Ct=Le(a.nextSibling),l=a.data==="F!";break t}}Kn(l)}l=!1}l&&(e=n[0])}}return n=ne(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:hr,lastRenderedState:e},n.queue=l,n=Lr.bind(null,ft,l),l.dispatch=n,l=kc(!1),u=ef.bind(null,ft,!1,l.queue),l=ne(),a={state:e,dispatch:null,action:t,pending:null},l.queue=a,n=By.bind(null,ft,a,u,n),a.dispatch=n,l.memoizedState=t,[e,n,!1]}function yr(t){var e=qt();return vr(e,Rt,t)}function vr(t,e,n){if(e=Jc(t,e,hr)[0],t=Xu(ke)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=Da(e)}catch(o){throw o===Ea?qu:o}else l=e;e=qt();var a=e.queue,u=a.dispatch;return n!==e.memoizedState&&(ft.flags|=2048,xl(9,Qu(),Hy.bind(null,a,n),null)),[l,u,t]}function Hy(t,e){t.action=e}function gr(t){var e=qt(),n=Rt;if(n!==null)return vr(e,n,t);qt(),e=e.memoizedState,n=qt();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function xl(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=ft.updateQueue,e===null&&(e=Zc(),ft.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Qu(){return{destroy:void 0,resource:void 0}}function pr(){return qt().memoizedState}function Zu(t,e,n,l){var a=ne();l=l===void 0?null:l,ft.flags|=t,a.memoizedState=xl(1|e,Qu(),n,l)}function xa(t,e,n,l){var a=qt();l=l===void 0?null:l;var u=a.memoizedState.inst;Rt!==null&&l!==null&&Gc(l,Rt.memoizedState.deps)?a.memoizedState=xl(e,u,n,l):(ft.flags|=t,a.memoizedState=xl(1|e,u,n,l))}function Sr(t,e){Zu(8390656,8,t,e)}function _r(t,e){xa(2048,8,t,e)}function br(t,e){return xa(4,2,t,e)}function Rr(t,e){return xa(4,4,t,e)}function Er(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Tr(t,e,n){n=n!=null?n.concat([t]):null,xa(4,4,Er.bind(null,e,t),n)}function Wc(){}function Mr(t,e){var n=qt();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Gc(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function Ar(t,e){var n=qt();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Gc(e,l[1]))return l[0];if(l=t(),Pn){cn(!0);try{t()}finally{cn(!1)}}return n.memoizedState=[l,e],l}function Fc(t,e,n){return n===void 0||mn&1073741824?t.memoizedState=e:(t.memoizedState=n,t=Dd(),ft.lanes|=t,bn|=t,n)}function Or(t,e,n,l){return fe(n,e)?n:Ol.current!==null?(t=Fc(t,n,l),fe(t,e)||(wt=!0),t):mn&42?(t=Dd(),ft.lanes|=t,bn|=t,e):(wt=!0,t.memoizedState=n)}function zr(t,e,n,l,a){var u=Y.p;Y.p=u!==0&&8>u?u:8;var o=C.T,h={};C.T=h,ef(t,!1,e,n);try{var S=a(),z=C.S;if(z!==null&&z(h,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var L=Uy(S,l);Ca(t,e,L,he(t))}else Ca(t,e,l,he(t))}catch(B){Ca(t,e,{then:function(){},status:"rejected",reason:B},he())}finally{Y.p=u,C.T=o}}function qy(){}function Ic(t,e,n,l){if(t.tag!==5)throw Error(f(476));var a=Dr(t).queue;zr(t,a,e,P,n===null?qy:function(){return xr(t),n(l)})}function Dr(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:P},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ke,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function xr(t){var e=Dr(t).next.queue;Ca(t,e,{},he())}function tf(){return Wt(ka)}function Cr(){return qt().memoizedState}function Ur(){return qt().memoizedState}function jy(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=he();t=dn(n);var l=hn(e,t,n);l!==null&&(me(l,e,n),Ma(l,e,n)),e={cache:xc()},t.payload=e;return}e=e.return}}function Yy(t,e,n){var l=he();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ku(t)?Nr(e,n):(n=_c(t,e,n,l),n!==null&&(me(n,t,l),Br(n,e,l)))}function Lr(t,e,n){var l=he();Ca(t,e,n,l)}function Ca(t,e,n,l){var a={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ku(t))Nr(e,a);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var o=e.lastRenderedState,h=u(o,n);if(a.hasEagerState=!0,a.eagerState=h,fe(h,o))return Du(t,e,a,0),At===null&&zu(),!1}catch{}finally{}if(n=_c(t,e,a,l),n!==null)return me(n,t,l),Br(n,e,l),!0}return!1}function ef(t,e,n,l){if(l={lane:2,revertLane:Nf(),action:l,hasEagerState:!1,eagerState:null,next:null},Ku(t)){if(e)throw Error(f(479))}else e=_c(t,n,l,2),e!==null&&me(e,t,2)}function Ku(t){var e=t.alternate;return t===ft||e!==null&&e===ft}function Nr(t,e){zl=Gu=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Br(t,e,n){if(n&4194048){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,ws(t,n)}}var Ju={readContext:Wt,use:wu,useCallback:Nt,useContext:Nt,useEffect:Nt,useImperativeHandle:Nt,useLayoutEffect:Nt,useInsertionEffect:Nt,useMemo:Nt,useReducer:Nt,useRef:Nt,useState:Nt,useDebugValue:Nt,useDeferredValue:Nt,useTransition:Nt,useSyncExternalStore:Nt,useId:Nt,useHostTransitionStatus:Nt,useFormState:Nt,useActionState:Nt,useOptimistic:Nt,useMemoCache:Nt,useCacheRefresh:Nt},Hr={readContext:Wt,use:wu,useCallback:function(t,e){return ne().memoizedState=[t,e===void 0?null:e],t},useContext:Wt,useEffect:Sr,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Zu(4194308,4,Er.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Zu(4194308,4,t,e)},useInsertionEffect:function(t,e){Zu(4,2,t,e)},useMemo:function(t,e){var n=ne();e=e===void 0?null:e;var l=t();if(Pn){cn(!0);try{t()}finally{cn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=ne();if(n!==void 0){var a=n(e);if(Pn){cn(!0);try{n(e)}finally{cn(!1)}}}else a=e;return l.memoizedState=l.baseState=a,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:a},l.queue=t,t=t.dispatch=Yy.bind(null,ft,t),[l.memoizedState,t]},useRef:function(t){var e=ne();return t={current:t},e.memoizedState=t},useState:function(t){t=kc(t);var e=t.queue,n=Lr.bind(null,ft,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Wc,useDeferredValue:function(t,e){var n=ne();return Fc(n,t,e)},useTransition:function(){var t=kc(!1);return t=zr.bind(null,ft,t.queue,!0,!1),ne().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=ft,a=ne();if(pt){if(n===void 0)throw Error(f(407));n=n()}else{if(n=e(),At===null)throw Error(f(349));yt&124||lr(l,e,n)}a.memoizedState=n;var u={value:n,getSnapshot:e};return a.queue=u,Sr(ur.bind(null,l,u,t),[t]),l.flags|=2048,xl(9,Qu(),ar.bind(null,l,u,n,e),null),n},useId:function(){var t=ne(),e=At.identifierPrefix;if(pt){var n=Ke,l=Ze;n=(l&~(1<<32-ce(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Vu++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Ly++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:tf,useFormState:mr,useActionState:mr,useOptimistic:function(t){var e=ne();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=ef.bind(null,ft,!0,n),n.dispatch=e,[t,e]},useMemoCache:Kc,useCacheRefresh:function(){return ne().memoizedState=jy.bind(null,ft)}},qr={readContext:Wt,use:wu,useCallback:Mr,useContext:Wt,useEffect:_r,useImperativeHandle:Tr,useInsertionEffect:br,useLayoutEffect:Rr,useMemo:Ar,useReducer:Xu,useRef:pr,useState:function(){return Xu(ke)},useDebugValue:Wc,useDeferredValue:function(t,e){var n=qt();return Or(n,Rt.memoizedState,t,e)},useTransition:function(){var t=Xu(ke)[0],e=qt().memoizedState;return[typeof t=="boolean"?t:Da(t),e]},useSyncExternalStore:nr,useId:Cr,useHostTransitionStatus:tf,useFormState:yr,useActionState:yr,useOptimistic:function(t,e){var n=qt();return fr(n,Rt,t,e)},useMemoCache:Kc,useCacheRefresh:Ur},Gy={readContext:Wt,use:wu,useCallback:Mr,useContext:Wt,useEffect:_r,useImperativeHandle:Tr,useInsertionEffect:br,useLayoutEffect:Rr,useMemo:Ar,useReducer:$c,useRef:pr,useState:function(){return $c(ke)},useDebugValue:Wc,useDeferredValue:function(t,e){var n=qt();return Rt===null?Fc(n,t,e):Or(n,Rt.memoizedState,t,e)},useTransition:function(){var t=$c(ke)[0],e=qt().memoizedState;return[typeof t=="boolean"?t:Da(t),e]},useSyncExternalStore:nr,useId:Cr,useHostTransitionStatus:tf,useFormState:gr,useActionState:gr,useOptimistic:function(t,e){var n=qt();return Rt!==null?fr(n,Rt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Kc,useCacheRefresh:Ur},Cl=null,Ua=0;function $u(t){var e=Ua;return Ua+=1,Cl===null&&(Cl=[]),$o(Cl,t,e)}function La(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ku(t,e){throw e.$$typeof===g?Error(f(525)):(t=Object.prototype.toString.call(e),Error(f(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function jr(t){var e=t._init;return e(t._payload)}function Yr(t){function e(T,E){if(t){var O=T.deletions;O===null?(T.deletions=[E],T.flags|=16):O.push(E)}}function n(T,E){if(!t)return null;for(;E!==null;)e(T,E),E=E.sibling;return null}function l(T){for(var E=new Map;T!==null;)T.key!==null?E.set(T.key,T):E.set(T.index,T),T=T.sibling;return E}function a(T,E){return T=Qe(T,E),T.index=0,T.sibling=null,T}function u(T,E,O){return T.index=O,t?(O=T.alternate,O!==null?(O=O.index,O<E?(T.flags|=67108866,E):O):(T.flags|=67108866,E)):(T.flags|=1048576,E)}function o(T){return t&&T.alternate===null&&(T.flags|=67108866),T}function h(T,E,O,N){return E===null||E.tag!==6?(E=Rc(O,T.mode,N),E.return=T,E):(E=a(E,O),E.return=T,E)}function S(T,E,O,N){var K=O.type;return K===A?L(T,E,O.props.children,N,O.key):E!==null&&(E.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===nt&&jr(K)===E.type)?(E=a(E,O.props),La(E,O),E.return=T,E):(E=Cu(O.type,O.key,O.props,null,T.mode,N),La(E,O),E.return=T,E)}function z(T,E,O,N){return E===null||E.tag!==4||E.stateNode.containerInfo!==O.containerInfo||E.stateNode.implementation!==O.implementation?(E=Ec(O,T.mode,N),E.return=T,E):(E=a(E,O.children||[]),E.return=T,E)}function L(T,E,O,N,K){return E===null||E.tag!==7?(E=wn(O,T.mode,N,K),E.return=T,E):(E=a(E,O),E.return=T,E)}function B(T,E,O){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=Rc(""+E,T.mode,O),E.return=T,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case _:return O=Cu(E.type,E.key,E.props,null,T.mode,O),La(O,E),O.return=T,O;case M:return E=Ec(E,T.mode,O),E.return=T,E;case nt:var N=E._init;return E=N(E._payload),B(T,E,O)}if(Lt(E)||mt(E))return E=wn(E,T.mode,O,null),E.return=T,E;if(typeof E.then=="function")return B(T,$u(E),O);if(E.$$typeof===X)return B(T,Bu(T,E),O);ku(T,E)}return null}function D(T,E,O,N){var K=E!==null?E.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return K!==null?null:h(T,E,""+O,N);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case _:return O.key===K?S(T,E,O,N):null;case M:return O.key===K?z(T,E,O,N):null;case nt:return K=O._init,O=K(O._payload),D(T,E,O,N)}if(Lt(O)||mt(O))return K!==null?null:L(T,E,O,N,null);if(typeof O.then=="function")return D(T,E,$u(O),N);if(O.$$typeof===X)return D(T,E,Bu(T,O),N);ku(T,O)}return null}function x(T,E,O,N,K){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return T=T.get(O)||null,h(E,T,""+N,K);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case _:return T=T.get(N.key===null?O:N.key)||null,S(E,T,N,K);case M:return T=T.get(N.key===null?O:N.key)||null,z(E,T,N,K);case nt:var ot=N._init;return N=ot(N._payload),x(T,E,O,N,K)}if(Lt(N)||mt(N))return T=T.get(O)||null,L(E,T,N,K,null);if(typeof N.then=="function")return x(T,E,O,$u(N),K);if(N.$$typeof===X)return x(T,E,O,Bu(E,N),K);ku(E,N)}return null}function tt(T,E,O,N){for(var K=null,ot=null,$=E,I=E=0,Qt=null;$!==null&&I<O.length;I++){$.index>I?(Qt=$,$=null):Qt=$.sibling;var vt=D(T,$,O[I],N);if(vt===null){$===null&&($=Qt);break}t&&$&&vt.alternate===null&&e(T,$),E=u(vt,E,I),ot===null?K=vt:ot.sibling=vt,ot=vt,$=Qt}if(I===O.length)return n(T,$),pt&&Qn(T,I),K;if($===null){for(;I<O.length;I++)$=B(T,O[I],N),$!==null&&(E=u($,E,I),ot===null?K=$:ot.sibling=$,ot=$);return pt&&Qn(T,I),K}for($=l($);I<O.length;I++)Qt=x($,T,I,O[I],N),Qt!==null&&(t&&Qt.alternate!==null&&$.delete(Qt.key===null?I:Qt.key),E=u(Qt,E,I),ot===null?K=Qt:ot.sibling=Qt,ot=Qt);return t&&$.forEach(function(xn){return e(T,xn)}),pt&&Qn(T,I),K}function F(T,E,O,N){if(O==null)throw Error(f(151));for(var K=null,ot=null,$=E,I=E=0,Qt=null,vt=O.next();$!==null&&!vt.done;I++,vt=O.next()){$.index>I?(Qt=$,$=null):Qt=$.sibling;var xn=D(T,$,vt.value,N);if(xn===null){$===null&&($=Qt);break}t&&$&&xn.alternate===null&&e(T,$),E=u(xn,E,I),ot===null?K=xn:ot.sibling=xn,ot=xn,$=Qt}if(vt.done)return n(T,$),pt&&Qn(T,I),K;if($===null){for(;!vt.done;I++,vt=O.next())vt=B(T,vt.value,N),vt!==null&&(E=u(vt,E,I),ot===null?K=vt:ot.sibling=vt,ot=vt);return pt&&Qn(T,I),K}for($=l($);!vt.done;I++,vt=O.next())vt=x($,T,I,vt.value,N),vt!==null&&(t&&vt.alternate!==null&&$.delete(vt.key===null?I:vt.key),E=u(vt,E,I),ot===null?K=vt:ot.sibling=vt,ot=vt);return t&&$.forEach(function(Vv){return e(T,Vv)}),pt&&Qn(T,I),K}function Tt(T,E,O,N){if(typeof O=="object"&&O!==null&&O.type===A&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case _:t:{for(var K=O.key;E!==null;){if(E.key===K){if(K=O.type,K===A){if(E.tag===7){n(T,E.sibling),N=a(E,O.props.children),N.return=T,T=N;break t}}else if(E.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===nt&&jr(K)===E.type){n(T,E.sibling),N=a(E,O.props),La(N,O),N.return=T,T=N;break t}n(T,E);break}else e(T,E);E=E.sibling}O.type===A?(N=wn(O.props.children,T.mode,N,O.key),N.return=T,T=N):(N=Cu(O.type,O.key,O.props,null,T.mode,N),La(N,O),N.return=T,T=N)}return o(T);case M:t:{for(K=O.key;E!==null;){if(E.key===K)if(E.tag===4&&E.stateNode.containerInfo===O.containerInfo&&E.stateNode.implementation===O.implementation){n(T,E.sibling),N=a(E,O.children||[]),N.return=T,T=N;break t}else{n(T,E);break}else e(T,E);E=E.sibling}N=Ec(O,T.mode,N),N.return=T,T=N}return o(T);case nt:return K=O._init,O=K(O._payload),Tt(T,E,O,N)}if(Lt(O))return tt(T,E,O,N);if(mt(O)){if(K=mt(O),typeof K!="function")throw Error(f(150));return O=K.call(O),F(T,E,O,N)}if(typeof O.then=="function")return Tt(T,E,$u(O),N);if(O.$$typeof===X)return Tt(T,E,Bu(T,O),N);ku(T,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,E!==null&&E.tag===6?(n(T,E.sibling),N=a(E,O),N.return=T,T=N):(n(T,E),N=Rc(O,T.mode,N),N.return=T,T=N),o(T)):n(T,E)}return function(T,E,O,N){try{Ua=0;var K=Tt(T,E,O,N);return Cl=null,K}catch($){if($===Ea||$===qu)throw $;var ot=se(29,$,null,T.mode);return ot.lanes=N,ot.return=T,ot}finally{}}}var Ul=Yr(!0),Gr=Yr(!1),Te=q(null),He=null;function yn(t){var e=t.alternate;G(Yt,Yt.current&1),G(Te,t),He===null&&(e===null||Ol.current!==null||e.memoizedState!==null)&&(He=t)}function Vr(t){if(t.tag===22){if(G(Yt,Yt.current),G(Te,t),He===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(He=t)}}else vn()}function vn(){G(Yt,Yt.current),G(Te,Te.current)}function Pe(t){w(Te),He===t&&(He=null),w(Yt)}var Yt=q(0);function Pu(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Kf(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function nf(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:b({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var lf={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=he(),a=dn(l);a.payload=e,n!=null&&(a.callback=n),e=hn(t,a,l),e!==null&&(me(e,t,l),Ma(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=he(),a=dn(l);a.tag=1,a.payload=e,n!=null&&(a.callback=n),e=hn(t,a,l),e!==null&&(me(e,t,l),Ma(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=he(),l=dn(n);l.tag=2,e!=null&&(l.callback=e),e=hn(t,l,n),e!==null&&(me(e,t,n),Ma(e,t,n))}};function wr(t,e,n,l,a,u,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,u,o):e.prototype&&e.prototype.isPureReactComponent?!ya(n,l)||!ya(a,u):!0}function Xr(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&lf.enqueueReplaceState(e,e.state,null)}function Wn(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=b({},n));for(var a in t)n[a]===void 0&&(n[a]=t[a])}return n}var Wu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Qr(t){Wu(t)}function Zr(t){console.error(t)}function Kr(t){Wu(t)}function Fu(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Jr(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(a){setTimeout(function(){throw a})}}function af(t,e,n){return n=dn(n),n.tag=3,n.payload={element:null},n.callback=function(){Fu(t,e)},n}function $r(t){return t=dn(t),t.tag=3,t}function kr(t,e,n,l){var a=n.type.getDerivedStateFromError;if(typeof a=="function"){var u=l.value;t.payload=function(){return a(u)},t.callback=function(){Jr(e,n,l)}}var o=n.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(t.callback=function(){Jr(e,n,l),typeof a!="function"&&(Rn===null?Rn=new Set([this]):Rn.add(this));var h=l.stack;this.componentDidCatch(l.value,{componentStack:h!==null?h:""})})}function Vy(t,e,n,l,a){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&_a(e,n,a,!0),n=Te.current,n!==null){switch(n.tag){case 13:return He===null?Df():n.alternate===null&&Ut===0&&(Ut=3),n.flags&=-257,n.flags|=65536,n.lanes=a,l===Lc?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Cf(t,l,a)),!1;case 22:return n.flags|=65536,l===Lc?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Cf(t,l,a)),!1}throw Error(f(435,n.tag))}return Cf(t,l,a),Df(),!1}if(pt)return e=Te.current,e!==null?(!(e.flags&65536)&&(e.flags|=256),e.flags|=65536,e.lanes=a,l!==Ac&&(t=Error(f(422),{cause:l}),Sa(_e(t,n)))):(l!==Ac&&(e=Error(f(423),{cause:l}),Sa(_e(e,n))),t=t.current.alternate,t.flags|=65536,a&=-a,t.lanes|=a,l=_e(l,n),a=af(t.stateNode,l,a),Hc(t,a),Ut!==4&&(Ut=2)),!1;var u=Error(f(520),{cause:l});if(u=_e(u,n),Ga===null?Ga=[u]:Ga.push(u),Ut!==4&&(Ut=2),e===null)return!0;l=_e(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=a&-a,n.lanes|=t,t=af(n.stateNode,l,t),Hc(n,t),!1;case 1:if(e=n.type,u=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Rn===null||!Rn.has(u))))return n.flags|=65536,a&=-a,n.lanes|=a,a=$r(a),kr(a,t,n,l),Hc(n,a),!1}n=n.return}while(n!==null);return!1}var Pr=Error(f(461)),wt=!1;function Jt(t,e,n,l){e.child=t===null?Gr(e,null,n,l):Ul(e,t.child,n,l)}function Wr(t,e,n,l,a){n=n.render;var u=e.ref;if("ref"in l){var o={};for(var h in l)h!=="ref"&&(o[h]=l[h])}else o=l;return $n(e),l=Vc(t,e,n,o,u,a),h=wc(),t!==null&&!wt?(Xc(t,e,a),We(t,e,a)):(pt&&h&&Tc(e),e.flags|=1,Jt(t,e,l,a),e.child)}function Fr(t,e,n,l,a){if(t===null){var u=n.type;return typeof u=="function"&&!bc(u)&&u.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=u,Ir(t,e,u,l,a)):(t=Cu(n.type,null,l,e,e.mode,a),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!hf(t,a)){var o=u.memoizedProps;if(n=n.compare,n=n!==null?n:ya,n(o,l)&&t.ref===e.ref)return We(t,e,a)}return e.flags|=1,t=Qe(u,l),t.ref=e.ref,t.return=e,e.child=t}function Ir(t,e,n,l,a){if(t!==null){var u=t.memoizedProps;if(ya(u,l)&&t.ref===e.ref)if(wt=!1,e.pendingProps=l=u,hf(t,a))t.flags&131072&&(wt=!0);else return e.lanes=t.lanes,We(t,e,a)}return uf(t,e,n,l,a)}function td(t,e,n){var l=e.pendingProps,a=l.children,u=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if(e.flags&128){if(l=u!==null?u.baseLanes|n:n,t!==null){for(a=e.child=t.child,u=0;a!==null;)u=u|a.lanes|a.childLanes,a=a.sibling;e.childLanes=u&~l}else e.childLanes=0,e.child=null;return ed(t,e,l,n)}if(n&536870912)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Hu(e,u!==null?u.cachePool:null),u!==null?Io(e,u):jc(),Vr(e);else return e.lanes=e.childLanes=536870912,ed(t,e,u!==null?u.baseLanes|n:n,n)}else u!==null?(Hu(e,u.cachePool),Io(e,u),vn(),e.memoizedState=null):(t!==null&&Hu(e,null),jc(),vn());return Jt(t,e,a,n),e.child}function ed(t,e,n,l){var a=Uc();return a=a===null?null:{parent:jt._currentValue,pool:a},e.memoizedState={baseLanes:n,cachePool:a},t!==null&&Hu(e,null),jc(),Vr(e),t!==null&&_a(t,e,l,!0),null}function Iu(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(f(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function uf(t,e,n,l,a){return $n(e),n=Vc(t,e,n,l,void 0,a),l=wc(),t!==null&&!wt?(Xc(t,e,a),We(t,e,a)):(pt&&l&&Tc(e),e.flags|=1,Jt(t,e,n,a),e.child)}function nd(t,e,n,l,a,u){return $n(e),e.updateQueue=null,n=er(e,l,n,a),tr(t),l=wc(),t!==null&&!wt?(Xc(t,e,u),We(t,e,u)):(pt&&l&&Tc(e),e.flags|=1,Jt(t,e,n,u),e.child)}function ld(t,e,n,l,a){if($n(e),e.stateNode===null){var u=Rl,o=n.contextType;typeof o=="object"&&o!==null&&(u=Wt(o)),u=new n(l,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=lf,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=l,u.state=e.memoizedState,u.refs={},Nc(e),o=n.contextType,u.context=typeof o=="object"&&o!==null?Wt(o):Rl,u.state=e.memoizedState,o=n.getDerivedStateFromProps,typeof o=="function"&&(nf(e,n,o,l),u.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(o=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),o!==u.state&&lf.enqueueReplaceState(u,u.state,null),Oa(e,l,u,a),Aa(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){u=e.stateNode;var h=e.memoizedProps,S=Wn(n,h);u.props=S;var z=u.context,L=n.contextType;o=Rl,typeof L=="object"&&L!==null&&(o=Wt(L));var B=n.getDerivedStateFromProps;L=typeof B=="function"||typeof u.getSnapshotBeforeUpdate=="function",h=e.pendingProps!==h,L||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(h||z!==o)&&Xr(e,u,l,o),rn=!1;var D=e.memoizedState;u.state=D,Oa(e,l,u,a),Aa(),z=e.memoizedState,h||D!==z||rn?(typeof B=="function"&&(nf(e,n,B,l),z=e.memoizedState),(S=rn||wr(e,n,S,l,D,z,o))?(L||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=z),u.props=l,u.state=z,u.context=o,l=S):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{u=e.stateNode,Bc(t,e),o=e.memoizedProps,L=Wn(n,o),u.props=L,B=e.pendingProps,D=u.context,z=n.contextType,S=Rl,typeof z=="object"&&z!==null&&(S=Wt(z)),h=n.getDerivedStateFromProps,(z=typeof h=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o!==B||D!==S)&&Xr(e,u,l,S),rn=!1,D=e.memoizedState,u.state=D,Oa(e,l,u,a),Aa();var x=e.memoizedState;o!==B||D!==x||rn||t!==null&&t.dependencies!==null&&Nu(t.dependencies)?(typeof h=="function"&&(nf(e,n,h,l),x=e.memoizedState),(L=rn||wr(e,n,L,l,D,x,S)||t!==null&&t.dependencies!==null&&Nu(t.dependencies))?(z||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,x,S),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,x,S)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||o===t.memoizedProps&&D===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&D===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=x),u.props=l,u.state=x,u.context=S,l=L):(typeof u.componentDidUpdate!="function"||o===t.memoizedProps&&D===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&D===t.memoizedState||(e.flags|=1024),l=!1)}return u=l,Iu(t,e),l=(e.flags&128)!==0,u||l?(u=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&l?(e.child=Ul(e,t.child,null,a),e.child=Ul(e,null,n,a)):Jt(t,e,n,a),e.memoizedState=u.state,t=e.child):t=We(t,e,a),t}function ad(t,e,n,l){return pa(),e.flags|=256,Jt(t,e,n,l),e.child}var cf={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ff(t){return{baseLanes:t,cachePool:Zo()}}function sf(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Me),t}function ud(t,e,n){var l=e.pendingProps,a=!1,u=(e.flags&128)!==0,o;if((o=u)||(o=t!==null&&t.memoizedState===null?!1:(Yt.current&2)!==0),o&&(a=!0,e.flags&=-129),o=(e.flags&32)!==0,e.flags&=-33,t===null){if(pt){if(a?yn(e):vn(),pt){var h=Ct,S;if(S=h){t:{for(S=h,h=Be;S.nodeType!==8;){if(!h){h=null;break t}if(S=Le(S.nextSibling),S===null){h=null;break t}}h=S}h!==null?(e.memoizedState={dehydrated:h,treeContext:Xn!==null?{id:Ze,overflow:Ke}:null,retryLane:536870912,hydrationErrors:null},S=se(18,null,null,0),S.stateNode=h,S.return=e,e.child=S,It=e,Ct=null,S=!0):S=!1}S||Kn(e)}if(h=e.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return Kf(h)?e.lanes=32:e.lanes=536870912,null;Pe(e)}return h=l.children,l=l.fallback,a?(vn(),a=e.mode,h=ti({mode:"hidden",children:h},a),l=wn(l,a,n,null),h.return=e,l.return=e,h.sibling=l,e.child=h,a=e.child,a.memoizedState=ff(n),a.childLanes=sf(t,o,n),e.memoizedState=cf,l):(yn(e),of(e,h))}if(S=t.memoizedState,S!==null&&(h=S.dehydrated,h!==null)){if(u)e.flags&256?(yn(e),e.flags&=-257,e=rf(t,e,n)):e.memoizedState!==null?(vn(),e.child=t.child,e.flags|=128,e=null):(vn(),a=l.fallback,h=e.mode,l=ti({mode:"visible",children:l.children},h),a=wn(a,h,n,null),a.flags|=2,l.return=e,a.return=e,l.sibling=a,e.child=l,Ul(e,t.child,null,n),l=e.child,l.memoizedState=ff(n),l.childLanes=sf(t,o,n),e.memoizedState=cf,e=a);else if(yn(e),Kf(h)){if(o=h.nextSibling&&h.nextSibling.dataset,o)var z=o.dgst;o=z,l=Error(f(419)),l.stack="",l.digest=o,Sa({value:l,source:null,stack:null}),e=rf(t,e,n)}else if(wt||_a(t,e,n,!1),o=(n&t.childLanes)!==0,wt||o){if(o=At,o!==null&&(l=n&-n,l=l&42?1:Ki(l),l=l&(o.suspendedLanes|n)?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,bl(t,l),me(o,t,l),Pr;h.data==="$?"||Df(),e=rf(t,e,n)}else h.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=S.treeContext,Ct=Le(h.nextSibling),It=e,pt=!0,Zn=null,Be=!1,t!==null&&(Re[Ee++]=Ze,Re[Ee++]=Ke,Re[Ee++]=Xn,Ze=t.id,Ke=t.overflow,Xn=e),e=of(e,l.children),e.flags|=4096);return e}return a?(vn(),a=l.fallback,h=e.mode,S=t.child,z=S.sibling,l=Qe(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,z!==null?a=Qe(z,a):(a=wn(a,h,n,null),a.flags|=2),a.return=e,l.return=e,l.sibling=a,e.child=l,l=a,a=e.child,h=t.child.memoizedState,h===null?h=ff(n):(S=h.cachePool,S!==null?(z=jt._currentValue,S=S.parent!==z?{parent:z,pool:z}:S):S=Zo(),h={baseLanes:h.baseLanes|n,cachePool:S}),a.memoizedState=h,a.childLanes=sf(t,o,n),e.memoizedState=cf,l):(yn(e),n=t.child,t=n.sibling,n=Qe(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(o=e.deletions,o===null?(e.deletions=[t],e.flags|=16):o.push(t)),e.child=n,e.memoizedState=null,n)}function of(t,e){return e=ti({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ti(t,e){return t=se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function rf(t,e,n){return Ul(e,t.child,null,n),t=of(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function id(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),zc(t.return,e,n)}function df(t,e,n,l,a){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:a}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=n,u.tailMode=a)}function cd(t,e,n){var l=e.pendingProps,a=l.revealOrder,u=l.tail;if(Jt(t,e,l.children,n),l=Yt.current,l&2)l=l&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&id(t,n,e);else if(t.tag===19)id(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(G(Yt,l),a){case"forwards":for(n=e.child,a=null;n!==null;)t=n.alternate,t!==null&&Pu(t)===null&&(a=n),n=n.sibling;n=a,n===null?(a=e.child,e.child=null):(a=n.sibling,n.sibling=null),df(e,!1,a,n,u);break;case"backwards":for(n=null,a=e.child,e.child=null;a!==null;){if(t=a.alternate,t!==null&&Pu(t)===null){e.child=a;break}t=a.sibling,a.sibling=n,n=a,a=t}df(e,!0,n,null,u);break;case"together":df(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function We(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),bn|=e.lanes,!(n&e.childLanes))if(t!==null){if(_a(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(f(153));if(e.child!==null){for(t=e.child,n=Qe(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Qe(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function hf(t,e){return t.lanes&e?!0:(t=t.dependencies,!!(t!==null&&Nu(t)))}function wy(t,e,n){switch(e.tag){case 3:gt(e,e.stateNode.containerInfo),on(e,jt,t.memoizedState.cache),pa();break;case 27:case 5:Nn(e);break;case 4:gt(e,e.stateNode.containerInfo);break;case 10:on(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(yn(e),e.flags|=128,null):n&e.child.childLanes?ud(t,e,n):(yn(e),t=We(t,e,n),t!==null?t.sibling:null);yn(e);break;case 19:var a=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(_a(t,e,n,!1),l=(n&e.childLanes)!==0),a){if(l)return cd(t,e,n);e.flags|=128}if(a=e.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),G(Yt,Yt.current),l)break;return null;case 22:case 23:return e.lanes=0,td(t,e,n);case 24:on(e,jt,t.memoizedState.cache)}return We(t,e,n)}function fd(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)wt=!0;else{if(!hf(t,n)&&!(e.flags&128))return wt=!1,wy(t,e,n);wt=!!(t.flags&131072)}else wt=!1,pt&&e.flags&1048576&&jo(e,Lu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,a=l._init;if(l=a(l._payload),e.type=l,typeof l=="function")bc(l)?(t=Wn(l,t),e.tag=1,e=ld(null,e,l,t,n)):(e.tag=0,e=uf(null,e,l,t,n));else{if(l!=null){if(a=l.$$typeof,a===J){e.tag=11,e=Wr(null,e,l,t,n);break t}else if(a===et){e.tag=14,e=Fr(null,e,l,t,n);break t}}throw e=Dt(l)||l,Error(f(306,e,""))}}return e;case 0:return uf(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,a=Wn(l,e.pendingProps),ld(t,e,l,a,n);case 3:t:{if(gt(e,e.stateNode.containerInfo),t===null)throw Error(f(387));l=e.pendingProps;var u=e.memoizedState;a=u.element,Bc(t,e),Oa(e,l,null,n);var o=e.memoizedState;if(l=o.cache,on(e,jt,l),l!==u.cache&&Dc(e,[jt],n,!0),Aa(),l=o.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:o.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=ad(t,e,l,n);break t}else if(l!==a){a=_e(Error(f(424)),e),Sa(a),e=ad(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ct=Le(t.firstChild),It=e,pt=!0,Zn=null,Be=!0,n=Gr(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(pa(),l===a){e=We(t,e,n);break t}Jt(t,e,l,n)}e=e.child}return e;case 26:return Iu(t,e),t===null?(n=dh(e.type,null,e.pendingProps,null))?e.memoizedState=n:pt||(n=e.type,t=e.pendingProps,l=mi(W.current).createElement(n),l[Pt]=e,l[te]=t,kt(l,n,t),Vt(l),e.stateNode=l):e.memoizedState=dh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Nn(e),t===null&&pt&&(l=e.stateNode=sh(e.type,e.pendingProps,W.current),It=e,Be=!0,a=Ct,Mn(e.type)?(Jf=a,Ct=Le(l.firstChild)):Ct=a),Jt(t,e,e.pendingProps.children,n),Iu(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&pt&&((a=l=Ct)&&(l=vv(l,e.type,e.pendingProps,Be),l!==null?(e.stateNode=l,It=e,Ct=Le(l.firstChild),Be=!1,a=!0):a=!1),a||Kn(e)),Nn(e),a=e.type,u=e.pendingProps,o=t!==null?t.memoizedProps:null,l=u.children,Xf(a,u)?l=null:o!==null&&Xf(a,o)&&(e.flags|=32),e.memoizedState!==null&&(a=Vc(t,e,Ny,null,null,n),ka._currentValue=a),Iu(t,e),Jt(t,e,l,n),e.child;case 6:return t===null&&pt&&((t=n=Ct)&&(n=gv(n,e.pendingProps,Be),n!==null?(e.stateNode=n,It=e,Ct=null,t=!0):t=!1),t||Kn(e)),null;case 13:return ud(t,e,n);case 4:return gt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Ul(e,null,l,n):Jt(t,e,l,n),e.child;case 11:return Wr(t,e,e.type,e.pendingProps,n);case 7:return Jt(t,e,e.pendingProps,n),e.child;case 8:return Jt(t,e,e.pendingProps.children,n),e.child;case 12:return Jt(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,on(e,e.type,l.value),Jt(t,e,l.children,n),e.child;case 9:return a=e.type._context,l=e.pendingProps.children,$n(e),a=Wt(a),l=l(a),e.flags|=1,Jt(t,e,l,n),e.child;case 14:return Fr(t,e,e.type,e.pendingProps,n);case 15:return Ir(t,e,e.type,e.pendingProps,n);case 19:return cd(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=ti(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Qe(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return td(t,e,n);case 24:return $n(e),l=Wt(jt),t===null?(a=Uc(),a===null&&(a=At,u=xc(),a.pooledCache=u,u.refCount++,u!==null&&(a.pooledCacheLanes|=n),a=u),e.memoizedState={parent:l,cache:a},Nc(e),on(e,jt,a)):(t.lanes&n&&(Bc(t,e),Oa(e,null,null,n),Aa()),a=t.memoizedState,u=e.memoizedState,a.parent!==l?(a={parent:l,cache:l},e.memoizedState=a,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=a),on(e,jt,l)):(l=u.cache,on(e,jt,l),l!==a.cache&&Dc(e,[jt],n,!0))),Jt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(f(156,e.tag))}function Fe(t){t.flags|=4}function sd(t,e){if(e.type!=="stylesheet"||e.state.loading&4)t.flags&=-16777217;else if(t.flags|=16777216,!gh(e)){if(e=Te.current,e!==null&&((yt&4194048)===yt?He!==null:(yt&62914560)!==yt&&!(yt&536870912)||e!==He))throw Ta=Lc,Ko;t.flags|=8192}}function ei(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Gs():536870912,t.lanes|=e,Hl|=e)}function Na(t,e){if(!pt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function xt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var a=t.child;a!==null;)n|=a.lanes|a.childLanes,l|=a.subtreeFlags&65011712,l|=a.flags&65011712,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)n|=a.lanes|a.childLanes,l|=a.subtreeFlags,l|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function Xy(t,e,n){var l=e.pendingProps;switch(Mc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return xt(e),null;case 1:return xt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),$e(jt),ye(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(ga(e)?Fe(e):t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,Vo())),xt(e),null;case 26:return n=e.memoizedState,t===null?(Fe(e),n!==null?(xt(e),sd(e,n)):(xt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(Fe(e),xt(e),sd(e,n)):(xt(e),e.flags&=-16777217):(t.memoizedProps!==l&&Fe(e),xt(e),e.flags&=-16777217),null;case 27:ve(e),n=W.current;var a=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Fe(e);else{if(!l){if(e.stateNode===null)throw Error(f(166));return xt(e),null}t=k.current,ga(e)?Yo(e):(t=sh(a,l,n),e.stateNode=t,Fe(e))}return xt(e),null;case 5:if(ve(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Fe(e);else{if(!l){if(e.stateNode===null)throw Error(f(166));return xt(e),null}if(t=k.current,ga(e))Yo(e);else{switch(a=mi(W.current),t){case 1:t=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?a.createElement("select",{is:l.is}):a.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?a.createElement(n,{is:l.is}):a.createElement(n)}}t[Pt]=e,t[te]=l;t:for(a=e.child;a!==null;){if(a.tag===5||a.tag===6)t.appendChild(a.stateNode);else if(a.tag!==4&&a.tag!==27&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;a=a.return}a.sibling.return=a.return,a=a.sibling}e.stateNode=t;t:switch(kt(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Fe(e)}}return xt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&Fe(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(f(166));if(t=W.current,ga(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,a=It,a!==null)switch(a.tag){case 27:case 5:l=a.memoizedProps}t[Pt]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||nh(t.nodeValue,n)),t||Kn(e)}else t=mi(t).createTextNode(l),t[Pt]=e,e.stateNode=t}return xt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(a=ga(e),l!==null&&l.dehydrated!==null){if(t===null){if(!a)throw Error(f(318));if(a=e.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(f(317));a[Pt]=e}else pa(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;xt(e),a=!1}else a=Vo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=a),a=!0;if(!a)return e.flags&256?(Pe(e),e):(Pe(e),null)}if(Pe(e),e.flags&128)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,a=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(a=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==a&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),ei(e,e.updateQueue),xt(e),null;case 4:return ye(),t===null&&jf(e.stateNode.containerInfo),xt(e),null;case 10:return $e(e.type),xt(e),null;case 19:if(w(Yt),a=e.memoizedState,a===null)return xt(e),null;if(l=(e.flags&128)!==0,u=a.rendering,u===null)if(l)Na(a,!1);else{if(Ut!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(u=Pu(t),u!==null){for(e.flags|=128,Na(a,!1),t=u.updateQueue,e.updateQueue=t,ei(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)qo(n,t),n=n.sibling;return G(Yt,Yt.current&1|2),e.child}t=t.sibling}a.tail!==null&&ge()>ai&&(e.flags|=128,l=!0,Na(a,!1),e.lanes=4194304)}else{if(!l)if(t=Pu(u),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,ei(e,t),Na(a,!0),a.tail===null&&a.tailMode==="hidden"&&!u.alternate&&!pt)return xt(e),null}else 2*ge()-a.renderingStartTime>ai&&n!==536870912&&(e.flags|=128,l=!0,Na(a,!1),e.lanes=4194304);a.isBackwards?(u.sibling=e.child,e.child=u):(t=a.last,t!==null?t.sibling=u:e.child=u,a.last=u)}return a.tail!==null?(e=a.tail,a.rendering=e,a.tail=e.sibling,a.renderingStartTime=ge(),e.sibling=null,t=Yt.current,G(Yt,l?t&1|2:t&1),e):(xt(e),null);case 22:case 23:return Pe(e),Yc(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?n&536870912&&!(e.flags&128)&&(xt(e),e.subtreeFlags&6&&(e.flags|=8192)):xt(e),n=e.updateQueue,n!==null&&ei(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&w(kn),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),$e(jt),xt(e),null;case 25:return null;case 30:return null}throw Error(f(156,e.tag))}function Qy(t,e){switch(Mc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return $e(jt),ye(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return ve(e),null;case 13:if(Pe(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(f(340));pa()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return w(Yt),null;case 4:return ye(),null;case 10:return $e(e.type),null;case 22:case 23:return Pe(e),Yc(),t!==null&&w(kn),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return $e(jt),null;case 25:return null;default:return null}}function od(t,e){switch(Mc(e),e.tag){case 3:$e(jt),ye();break;case 26:case 27:case 5:ve(e);break;case 4:ye();break;case 13:Pe(e);break;case 19:w(Yt);break;case 10:$e(e.type);break;case 22:case 23:Pe(e),Yc(),t!==null&&w(kn);break;case 24:$e(jt)}}function Ba(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var a=l.next;n=a;do{if((n.tag&t)===t){l=void 0;var u=n.create,o=n.inst;l=u(),o.destroy=l}n=n.next}while(n!==a)}}catch(h){Mt(e,e.return,h)}}function gn(t,e,n){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var u=a.next;l=u;do{if((l.tag&t)===t){var o=l.inst,h=o.destroy;if(h!==void 0){o.destroy=void 0,a=e;var S=n,z=h;try{z()}catch(L){Mt(a,S,L)}}}l=l.next}while(l!==u)}}catch(L){Mt(e,e.return,L)}}function rd(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Fo(e,n)}catch(l){Mt(t,t.return,l)}}}function dd(t,e,n){n.props=Wn(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){Mt(t,e,l)}}function Ha(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(a){Mt(t,e,a)}}function qe(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(a){Mt(t,e,a)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(a){Mt(t,e,a)}else n.current=null}function hd(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(a){Mt(t,t.return,a)}}function mf(t,e,n){try{var l=t.stateNode;rv(l,t.type,n,e),l[te]=e}catch(a){Mt(t,t.return,a)}}function md(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Mn(t.type)||t.tag===4}function yf(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||md(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Mn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function vf(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=hi));else if(l!==4&&(l===27&&Mn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(vf(t,e,n),t=t.sibling;t!==null;)vf(t,e,n),t=t.sibling}function ni(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&Mn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(ni(t,e,n),t=t.sibling;t!==null;)ni(t,e,n),t=t.sibling}function yd(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,a=e.attributes;a.length;)e.removeAttributeNode(a[0]);kt(e,l,n),e[Pt]=t,e[te]=n}catch(u){Mt(t,t.return,u)}}var Ie=!1,Bt=!1,gf=!1,vd=typeof WeakSet=="function"?WeakSet:Set,Xt=null;function Zy(t,e){if(t=t.containerInfo,Vf=_i,t=Oo(t),mc(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var a=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break t}var o=0,h=-1,S=-1,z=0,L=0,B=t,D=null;e:for(;;){for(var x;B!==n||a!==0&&B.nodeType!==3||(h=o+a),B!==u||l!==0&&B.nodeType!==3||(S=o+l),B.nodeType===3&&(o+=B.nodeValue.length),(x=B.firstChild)!==null;)D=B,B=x;for(;;){if(B===t)break e;if(D===n&&++z===a&&(h=o),D===u&&++L===l&&(S=o),(x=B.nextSibling)!==null)break;B=D,D=B.parentNode}B=x}n=h===-1||S===-1?null:{start:h,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(wf={focusedElem:t,selectionRange:n},_i=!1,Xt=e;Xt!==null;)if(e=Xt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Xt=t;else for(;Xt!==null;){switch(e=Xt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if(t&1024&&u!==null){t=void 0,n=e,a=u.memoizedProps,u=u.memoizedState,l=n.stateNode;try{var tt=Wn(n.type,a,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(tt,u),l.__reactInternalSnapshotBeforeUpdate=t}catch(F){Mt(n,n.return,F)}}break;case 3:if(t&1024){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)Zf(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Zf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(t&1024)throw Error(f(163))}if(t=e.sibling,t!==null){t.return=e.return,Xt=t;break}Xt=e.return}}function gd(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:pn(t,n),l&4&&Ba(5,n);break;case 1:if(pn(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(o){Mt(n,n.return,o)}else{var a=Wn(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(a,e,t.__reactInternalSnapshotBeforeUpdate)}catch(o){Mt(n,n.return,o)}}l&64&&rd(n),l&512&&Ha(n,n.return);break;case 3:if(pn(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Fo(t,e)}catch(o){Mt(n,n.return,o)}}break;case 27:e===null&&l&4&&yd(n);case 26:case 5:pn(t,n),e===null&&l&4&&hd(n),l&512&&Ha(n,n.return);break;case 12:pn(t,n);break;case 13:pn(t,n),l&4&&_d(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=tv.bind(null,n),pv(t,n))));break;case 22:if(l=n.memoizedState!==null||Ie,!l){e=e!==null&&e.memoizedState!==null||Bt,a=Ie;var u=Bt;Ie=l,(Bt=e)&&!u?Sn(t,n,(n.subtreeFlags&8772)!==0):pn(t,n),Ie=a,Bt=u}break;case 30:break;default:pn(t,n)}}function pd(t){var e=t.alternate;e!==null&&(t.alternate=null,pd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ki(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var zt=null,le=!1;function tn(t,e,n){for(n=n.child;n!==null;)Sd(t,e,n),n=n.sibling}function Sd(t,e,n){if(ie&&typeof ie.onCommitFiberUnmount=="function")try{ie.onCommitFiberUnmount(la,n)}catch{}switch(n.tag){case 26:Bt||qe(n,e),tn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Bt||qe(n,e);var l=zt,a=le;Mn(n.type)&&(zt=n.stateNode,le=!1),tn(t,e,n),Za(n.stateNode),zt=l,le=a;break;case 5:Bt||qe(n,e);case 6:if(l=zt,a=le,zt=null,tn(t,e,n),zt=l,le=a,zt!==null)if(le)try{(zt.nodeType===9?zt.body:zt.nodeName==="HTML"?zt.ownerDocument.body:zt).removeChild(n.stateNode)}catch(u){Mt(n,e,u)}else try{zt.removeChild(n.stateNode)}catch(u){Mt(n,e,u)}break;case 18:zt!==null&&(le?(t=zt,ch(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Ia(t)):ch(zt,n.stateNode));break;case 4:l=zt,a=le,zt=n.stateNode.containerInfo,le=!0,tn(t,e,n),zt=l,le=a;break;case 0:case 11:case 14:case 15:Bt||gn(2,n,e),Bt||gn(4,n,e),tn(t,e,n);break;case 1:Bt||(qe(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&dd(n,e,l)),tn(t,e,n);break;case 21:tn(t,e,n);break;case 22:Bt=(l=Bt)||n.memoizedState!==null,tn(t,e,n),Bt=l;break;default:tn(t,e,n)}}function _d(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ia(t)}catch(n){Mt(e,e.return,n)}}function Ky(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new vd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new vd),e;default:throw Error(f(435,t.tag))}}function pf(t,e){var n=Ky(t);e.forEach(function(l){var a=ev.bind(null,t,l);n.has(l)||(n.add(l),l.then(a,a))})}function oe(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var a=n[l],u=t,o=e,h=o;t:for(;h!==null;){switch(h.tag){case 27:if(Mn(h.type)){zt=h.stateNode,le=!1;break t}break;case 5:zt=h.stateNode,le=!1;break t;case 3:case 4:zt=h.stateNode.containerInfo,le=!0;break t}h=h.return}if(zt===null)throw Error(f(160));Sd(u,o,a),zt=null,le=!1,u=a.alternate,u!==null&&(u.return=null),a.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)bd(e,t),e=e.sibling}var Ue=null;function bd(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:oe(e,t),re(t),l&4&&(gn(3,t,t.return),Ba(3,t),gn(5,t,t.return));break;case 1:oe(e,t),re(t),l&512&&(Bt||n===null||qe(n,n.return)),l&64&&Ie&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var a=Ue;if(oe(e,t),re(t),l&512&&(Bt||n===null||qe(n,n.return)),l&4){var u=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,a=a.ownerDocument||a;e:switch(l){case"title":u=a.getElementsByTagName("title")[0],(!u||u[ia]||u[Pt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=a.createElement(l),a.head.insertBefore(u,a.querySelector("head > title"))),kt(u,l,n),u[Pt]=t,Vt(u),l=u;break t;case"link":var o=yh("link","href",a).get(l+(n.href||""));if(o){for(var h=0;h<o.length;h++)if(u=o[h],u.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&u.getAttribute("rel")===(n.rel==null?null:n.rel)&&u.getAttribute("title")===(n.title==null?null:n.title)&&u.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){o.splice(h,1);break e}}u=a.createElement(l),kt(u,l,n),a.head.appendChild(u);break;case"meta":if(o=yh("meta","content",a).get(l+(n.content||""))){for(h=0;h<o.length;h++)if(u=o[h],u.getAttribute("content")===(n.content==null?null:""+n.content)&&u.getAttribute("name")===(n.name==null?null:n.name)&&u.getAttribute("property")===(n.property==null?null:n.property)&&u.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&u.getAttribute("charset")===(n.charSet==null?null:n.charSet)){o.splice(h,1);break e}}u=a.createElement(l),kt(u,l,n),a.head.appendChild(u);break;default:throw Error(f(468,l))}u[Pt]=t,Vt(u),l=u}t.stateNode=l}else vh(a,t.type,t.stateNode);else t.stateNode=mh(a,l,t.memoizedProps);else u!==l?(u===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):u.count--,l===null?vh(a,t.type,t.stateNode):mh(a,l,t.memoizedProps)):l===null&&t.stateNode!==null&&mf(t,t.memoizedProps,n.memoizedProps)}break;case 27:oe(e,t),re(t),l&512&&(Bt||n===null||qe(n,n.return)),n!==null&&l&4&&mf(t,t.memoizedProps,n.memoizedProps);break;case 5:if(oe(e,t),re(t),l&512&&(Bt||n===null||qe(n,n.return)),t.flags&32){a=t.stateNode;try{ml(a,"")}catch(x){Mt(t,t.return,x)}}l&4&&t.stateNode!=null&&(a=t.memoizedProps,mf(t,a,n!==null?n.memoizedProps:a)),l&1024&&(gf=!0);break;case 6:if(oe(e,t),re(t),l&4){if(t.stateNode===null)throw Error(f(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(x){Mt(t,t.return,x)}}break;case 3:if(gi=null,a=Ue,Ue=yi(e.containerInfo),oe(e,t),Ue=a,re(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Ia(e.containerInfo)}catch(x){Mt(t,t.return,x)}gf&&(gf=!1,Rd(t));break;case 4:l=Ue,Ue=yi(t.stateNode.containerInfo),oe(e,t),re(t),Ue=l;break;case 12:oe(e,t),re(t);break;case 13:oe(e,t),re(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Tf=ge()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,pf(t,l)));break;case 22:a=t.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,z=Ie,L=Bt;if(Ie=z||a,Bt=L||S,oe(e,t),Bt=L,Ie=z,re(t),l&8192)t:for(e=t.stateNode,e._visibility=a?e._visibility&-2:e._visibility|1,a&&(n===null||S||Ie||Bt||Fn(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){S=n=e;try{if(u=S.stateNode,a)o=u.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{h=S.stateNode;var B=S.memoizedProps.style,D=B!=null&&B.hasOwnProperty("display")?B.display:null;h.style.display=D==null||typeof D=="boolean"?"":(""+D).trim()}}catch(x){Mt(S,S.return,x)}}}else if(e.tag===6){if(n===null){S=e;try{S.stateNode.nodeValue=a?"":S.memoizedProps}catch(x){Mt(S,S.return,x)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,pf(t,n))));break;case 19:oe(e,t),re(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,pf(t,l)));break;case 30:break;case 21:break;default:oe(e,t),re(t)}}function re(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(md(l)){n=l;break}l=l.return}if(n==null)throw Error(f(160));switch(n.tag){case 27:var a=n.stateNode,u=yf(t);ni(t,u,a);break;case 5:var o=n.stateNode;n.flags&32&&(ml(o,""),n.flags&=-33);var h=yf(t);ni(t,h,o);break;case 3:case 4:var S=n.stateNode.containerInfo,z=yf(t);vf(t,z,S);break;default:throw Error(f(161))}}catch(L){Mt(t,t.return,L)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Rd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Rd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function pn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)gd(t,e.alternate,e),e=e.sibling}function Fn(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:gn(4,e,e.return),Fn(e);break;case 1:qe(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&dd(e,e.return,n),Fn(e);break;case 27:Za(e.stateNode);case 26:case 5:qe(e,e.return),Fn(e);break;case 22:e.memoizedState===null&&Fn(e);break;case 30:Fn(e);break;default:Fn(e)}t=t.sibling}}function Sn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,a=t,u=e,o=u.flags;switch(u.tag){case 0:case 11:case 15:Sn(a,u,n),Ba(4,u);break;case 1:if(Sn(a,u,n),l=u,a=l.stateNode,typeof a.componentDidMount=="function")try{a.componentDidMount()}catch(z){Mt(l,l.return,z)}if(l=u,a=l.updateQueue,a!==null){var h=l.stateNode;try{var S=a.shared.hiddenCallbacks;if(S!==null)for(a.shared.hiddenCallbacks=null,a=0;a<S.length;a++)Wo(S[a],h)}catch(z){Mt(l,l.return,z)}}n&&o&64&&rd(u),Ha(u,u.return);break;case 27:yd(u);case 26:case 5:Sn(a,u,n),n&&l===null&&o&4&&hd(u),Ha(u,u.return);break;case 12:Sn(a,u,n);break;case 13:Sn(a,u,n),n&&o&4&&_d(a,u);break;case 22:u.memoizedState===null&&Sn(a,u,n),Ha(u,u.return);break;case 30:break;default:Sn(a,u,n)}e=e.sibling}}function Sf(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&ba(n))}function _f(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ba(t))}function je(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ed(t,e,n,l),e=e.sibling}function Ed(t,e,n,l){var a=e.flags;switch(e.tag){case 0:case 11:case 15:je(t,e,n,l),a&2048&&Ba(9,e);break;case 1:je(t,e,n,l);break;case 3:je(t,e,n,l),a&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ba(t)));break;case 12:if(a&2048){je(t,e,n,l),t=e.stateNode;try{var u=e.memoizedProps,o=u.id,h=u.onPostCommit;typeof h=="function"&&h(o,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(S){Mt(e,e.return,S)}}else je(t,e,n,l);break;case 13:je(t,e,n,l);break;case 23:break;case 22:u=e.stateNode,o=e.alternate,e.memoizedState!==null?u._visibility&2?je(t,e,n,l):qa(t,e):u._visibility&2?je(t,e,n,l):(u._visibility|=2,Ll(t,e,n,l,(e.subtreeFlags&10256)!==0)),a&2048&&Sf(o,e);break;case 24:je(t,e,n,l),a&2048&&_f(e.alternate,e);break;default:je(t,e,n,l)}}function Ll(t,e,n,l,a){for(a=a&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,o=e,h=n,S=l,z=o.flags;switch(o.tag){case 0:case 11:case 15:Ll(u,o,h,S,a),Ba(8,o);break;case 23:break;case 22:var L=o.stateNode;o.memoizedState!==null?L._visibility&2?Ll(u,o,h,S,a):qa(u,o):(L._visibility|=2,Ll(u,o,h,S,a)),a&&z&2048&&Sf(o.alternate,o);break;case 24:Ll(u,o,h,S,a),a&&z&2048&&_f(o.alternate,o);break;default:Ll(u,o,h,S,a)}e=e.sibling}}function qa(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,a=l.flags;switch(l.tag){case 22:qa(n,l),a&2048&&Sf(l.alternate,l);break;case 24:qa(n,l),a&2048&&_f(l.alternate,l);break;default:qa(n,l)}e=e.sibling}}var ja=8192;function Nl(t){if(t.subtreeFlags&ja)for(t=t.child;t!==null;)Td(t),t=t.sibling}function Td(t){switch(t.tag){case 26:Nl(t),t.flags&ja&&t.memoizedState!==null&&Cv(Ue,t.memoizedState,t.memoizedProps);break;case 5:Nl(t);break;case 3:case 4:var e=Ue;Ue=yi(t.stateNode.containerInfo),Nl(t),Ue=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ja,ja=16777216,Nl(t),ja=e):Nl(t));break;default:Nl(t)}}function Md(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Ya(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Xt=l,Od(l,t)}Md(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ad(t),t=t.sibling}function Ad(t){switch(t.tag){case 0:case 11:case 15:Ya(t),t.flags&2048&&gn(9,t,t.return);break;case 3:Ya(t);break;case 12:Ya(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,li(t)):Ya(t);break;default:Ya(t)}}function li(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Xt=l,Od(l,t)}Md(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:gn(8,e,e.return),li(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,li(e));break;default:li(e)}t=t.sibling}}function Od(t,e){for(;Xt!==null;){var n=Xt;switch(n.tag){case 0:case 11:case 15:gn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ba(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Xt=l;else t:for(n=t;Xt!==null;){l=Xt;var a=l.sibling,u=l.return;if(pd(l),l===n){Xt=null;break t}if(a!==null){a.return=u,Xt=a;break t}Xt=u}}}var Jy={getCacheForType:function(t){var e=Wt(jt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},$y=typeof WeakMap=="function"?WeakMap:Map,_t=0,At=null,rt=null,yt=0,bt=0,de=null,_n=!1,Bl=!1,bf=!1,en=0,Ut=0,bn=0,In=0,Rf=0,Me=0,Hl=0,Ga=null,ae=null,Ef=!1,Tf=0,ai=1/0,ui=null,Rn=null,$t=0,En=null,ql=null,jl=0,Mf=0,Af=null,zd=null,Va=0,Of=null;function he(){if(_t&2&&yt!==0)return yt&-yt;if(C.T!==null){var t=Ml;return t!==0?t:Nf()}return Xs()}function Dd(){Me===0&&(Me=!(yt&536870912)||pt?Ys():536870912);var t=Te.current;return t!==null&&(t.flags|=32),Me}function me(t,e,n){(t===At&&(bt===2||bt===9)||t.cancelPendingCommit!==null)&&(Yl(t,0),Tn(t,yt,Me,!1)),ua(t,n),(!(_t&2)||t!==At)&&(t===At&&(!(_t&2)&&(In|=n),Ut===4&&Tn(t,yt,Me,!1)),Ye(t))}function xd(t,e,n){if(_t&6)throw Error(f(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||aa(t,e),a=l?Wy(t,e):xf(t,e,!0),u=l;do{if(a===0){Bl&&!l&&Tn(t,e,0,!1);break}else{if(n=t.current.alternate,u&&!ky(n)){a=xf(t,e,!1),u=!1;continue}if(a===2){if(u=e,t.errorRecoveryDisabledLanes&u)var o=0;else o=t.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){e=o;t:{var h=t;a=Ga;var S=h.current.memoizedState.isDehydrated;if(S&&(Yl(h,o).flags|=256),o=xf(h,o,!1),o!==2){if(bf&&!S){h.errorRecoveryDisabledLanes|=u,In|=u,a=4;break t}u=ae,ae=a,u!==null&&(ae===null?ae=u:ae.push.apply(ae,u))}a=o}if(u=!1,a!==2)continue}}if(a===1){Yl(t,0),Tn(t,e,0,!0);break}t:{switch(l=t,u=a,u){case 0:case 1:throw Error(f(345));case 4:if((e&4194048)!==e)break;case 6:Tn(l,e,Me,!_n);break t;case 2:ae=null;break;case 3:case 5:break;default:throw Error(f(329))}if((e&62914560)===e&&(a=Tf+300-ge(),10<a)){if(Tn(l,e,Me,!_n),vu(l,0,!0)!==0)break t;l.timeoutHandle=uh(Cd.bind(null,l,n,ae,ui,Ef,e,Me,In,Hl,_n,u,2,-0,0),a);break t}Cd(l,n,ae,ui,Ef,e,Me,In,Hl,_n,u,0,-0,0)}}break}while(!0);Ye(t)}function Cd(t,e,n,l,a,u,o,h,S,z,L,B,D,x){if(t.timeoutHandle=-1,B=e.subtreeFlags,(B&8192||(B&16785408)===16785408)&&($a={stylesheets:null,count:0,unsuspend:xv},Td(e),B=Uv(),B!==null)){t.cancelPendingCommit=B(jd.bind(null,t,e,u,n,l,a,o,h,S,L,1,D,x)),Tn(t,u,o,!z);return}jd(t,e,u,n,l,a,o,h,S)}function ky(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var a=n[l],u=a.getSnapshot;a=a.value;try{if(!fe(u(),a))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Tn(t,e,n,l){e&=~Rf,e&=~In,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var a=e;0<a;){var u=31-ce(a),o=1<<u;l[u]=-1,a&=~o}n!==0&&Vs(t,n,e)}function ii(){return _t&6?!0:(wa(0),!1)}function zf(){if(rt!==null){if(bt===0)var t=rt.return;else t=rt,Je=Jn=null,Qc(t),Cl=null,Ua=0,t=rt;for(;t!==null;)od(t.alternate,t),t=t.return;rt=null}}function Yl(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,hv(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),zf(),At=t,rt=n=Qe(t.current,null),yt=e,bt=0,de=null,_n=!1,Bl=aa(t,e),bf=!1,Hl=Me=Rf=In=bn=Ut=0,ae=Ga=null,Ef=!1,e&8&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var a=31-ce(l),u=1<<a;e|=t[a],l&=~u}return en=e,zu(),n}function Ud(t,e){ft=null,C.H=Ju,e===Ea||e===qu?(e=ko(),bt=3):e===Ko?(e=ko(),bt=4):bt=e===Pr?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,de=e,rt===null&&(Ut=1,Fu(t,_e(e,t.current)))}function Ld(){var t=C.H;return C.H=Ju,t===null?Ju:t}function Nd(){var t=C.A;return C.A=Jy,t}function Df(){Ut=4,_n||(yt&4194048)!==yt&&Te.current!==null||(Bl=!0),!(bn&134217727)&&!(In&134217727)||At===null||Tn(At,yt,Me,!1)}function xf(t,e,n){var l=_t;_t|=2;var a=Ld(),u=Nd();(At!==t||yt!==e)&&(ui=null,Yl(t,e)),e=!1;var o=Ut;t:do try{if(bt!==0&&rt!==null){var h=rt,S=de;switch(bt){case 8:zf(),o=6;break t;case 3:case 2:case 9:case 6:Te.current===null&&(e=!0);var z=bt;if(bt=0,de=null,Gl(t,h,S,z),n&&Bl){o=0;break t}break;default:z=bt,bt=0,de=null,Gl(t,h,S,z)}}Py(),o=Ut;break}catch(L){Ud(t,L)}while(!0);return e&&t.shellSuspendCounter++,Je=Jn=null,_t=l,C.H=a,C.A=u,rt===null&&(At=null,yt=0,zu()),o}function Py(){for(;rt!==null;)Bd(rt)}function Wy(t,e){var n=_t;_t|=2;var l=Ld(),a=Nd();At!==t||yt!==e?(ui=null,ai=ge()+500,Yl(t,e)):Bl=aa(t,e);t:do try{if(bt!==0&&rt!==null){e=rt;var u=de;e:switch(bt){case 1:bt=0,de=null,Gl(t,e,u,1);break;case 2:case 9:if(Jo(u)){bt=0,de=null,Hd(e);break}e=function(){bt!==2&&bt!==9||At!==t||(bt=7),Ye(t)},u.then(e,e);break t;case 3:bt=7;break t;case 4:bt=5;break t;case 7:Jo(u)?(bt=0,de=null,Hd(e)):(bt=0,de=null,Gl(t,e,u,7));break;case 5:var o=null;switch(rt.tag){case 26:o=rt.memoizedState;case 5:case 27:var h=rt;if(!o||gh(o)){bt=0,de=null;var S=h.sibling;if(S!==null)rt=S;else{var z=h.return;z!==null?(rt=z,ci(z)):rt=null}break e}}bt=0,de=null,Gl(t,e,u,5);break;case 6:bt=0,de=null,Gl(t,e,u,6);break;case 8:zf(),Ut=6;break t;default:throw Error(f(462))}}Fy();break}catch(L){Ud(t,L)}while(!0);return Je=Jn=null,C.H=l,C.A=a,_t=n,rt!==null?0:(At=null,yt=0,zu(),Ut)}function Fy(){for(;rt!==null&&!Xi();)Bd(rt)}function Bd(t){var e=fd(t.alternate,t,en);t.memoizedProps=t.pendingProps,e===null?ci(t):rt=e}function Hd(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=nd(n,e,e.pendingProps,e.type,void 0,yt);break;case 11:e=nd(n,e,e.pendingProps,e.type.render,e.ref,yt);break;case 5:Qc(e);default:od(n,e),e=rt=qo(e,en),e=fd(n,e,en)}t.memoizedProps=t.pendingProps,e===null?ci(t):rt=e}function Gl(t,e,n,l){Je=Jn=null,Qc(e),Cl=null,Ua=0;var a=e.return;try{if(Vy(t,a,e,n,yt)){Ut=1,Fu(t,_e(n,t.current)),rt=null;return}}catch(u){if(a!==null)throw rt=a,u;Ut=1,Fu(t,_e(n,t.current)),rt=null;return}e.flags&32768?(pt||l===1?t=!0:Bl||yt&536870912?t=!1:(_n=t=!0,(l===2||l===9||l===3||l===6)&&(l=Te.current,l!==null&&l.tag===13&&(l.flags|=16384))),qd(e,t)):ci(e)}function ci(t){var e=t;do{if(e.flags&32768){qd(e,_n);return}t=e.return;var n=Xy(e.alternate,e,en);if(n!==null){rt=n;return}if(e=e.sibling,e!==null){rt=e;return}rt=e=t}while(e!==null);Ut===0&&(Ut=5)}function qd(t,e){do{var n=Qy(t.alternate,t);if(n!==null){n.flags&=32767,rt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){rt=t;return}rt=t=n}while(t!==null);Ut=6,rt=null}function jd(t,e,n,l,a,u,o,h,S){t.cancelPendingCommit=null;do fi();while($t!==0);if(_t&6)throw Error(f(327));if(e!==null){if(e===t.current)throw Error(f(177));if(u=e.lanes|e.childLanes,u|=Sc,xm(t,n,u,o,h,S),t===At&&(rt=At=null,yt=0),ql=e,En=t,jl=n,Mf=u,Af=a,zd=l,e.subtreeFlags&10256||e.flags&10256?(t.callbackNode=null,t.callbackPriority=0,nv(Hn,function(){return Xd(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,e.subtreeFlags&13878||l){l=C.T,C.T=null,a=Y.p,Y.p=2,o=_t,_t|=4;try{Zy(t,e,n)}finally{_t=o,Y.p=a,C.T=l}}$t=1,Yd(),Gd(),Vd()}}function Yd(){if($t===1){$t=0;var t=En,e=ql,n=(e.flags&13878)!==0;if(e.subtreeFlags&13878||n){n=C.T,C.T=null;var l=Y.p;Y.p=2;var a=_t;_t|=4;try{bd(e,t);var u=wf,o=Oo(t.containerInfo),h=u.focusedElem,S=u.selectionRange;if(o!==h&&h&&h.ownerDocument&&Ao(h.ownerDocument.documentElement,h)){if(S!==null&&mc(h)){var z=S.start,L=S.end;if(L===void 0&&(L=z),"selectionStart"in h)h.selectionStart=z,h.selectionEnd=Math.min(L,h.value.length);else{var B=h.ownerDocument||document,D=B&&B.defaultView||window;if(D.getSelection){var x=D.getSelection(),tt=h.textContent.length,F=Math.min(S.start,tt),Tt=S.end===void 0?F:Math.min(S.end,tt);!x.extend&&F>Tt&&(o=Tt,Tt=F,F=o);var T=Mo(h,F),E=Mo(h,Tt);if(T&&E&&(x.rangeCount!==1||x.anchorNode!==T.node||x.anchorOffset!==T.offset||x.focusNode!==E.node||x.focusOffset!==E.offset)){var O=B.createRange();O.setStart(T.node,T.offset),x.removeAllRanges(),F>Tt?(x.addRange(O),x.extend(E.node,E.offset)):(O.setEnd(E.node,E.offset),x.addRange(O))}}}}for(B=[],x=h;x=x.parentNode;)x.nodeType===1&&B.push({element:x,left:x.scrollLeft,top:x.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<B.length;h++){var N=B[h];N.element.scrollLeft=N.left,N.element.scrollTop=N.top}}_i=!!Vf,wf=Vf=null}finally{_t=a,Y.p=l,C.T=n}}t.current=e,$t=2}}function Gd(){if($t===2){$t=0;var t=En,e=ql,n=(e.flags&8772)!==0;if(e.subtreeFlags&8772||n){n=C.T,C.T=null;var l=Y.p;Y.p=2;var a=_t;_t|=4;try{gd(t,e.alternate,e)}finally{_t=a,Y.p=l,C.T=n}}$t=3}}function Vd(){if($t===4||$t===3){$t=0,Qi();var t=En,e=ql,n=jl,l=zd;e.subtreeFlags&10256||e.flags&10256?$t=5:($t=0,ql=En=null,wd(t,t.pendingLanes));var a=t.pendingLanes;if(a===0&&(Rn=null),Ji(n),e=e.stateNode,ie&&typeof ie.onCommitFiberRoot=="function")try{ie.onCommitFiberRoot(la,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=C.T,a=Y.p,Y.p=2,C.T=null;try{for(var u=t.onRecoverableError,o=0;o<l.length;o++){var h=l[o];u(h.value,{componentStack:h.stack})}}finally{C.T=e,Y.p=a}}jl&3&&fi(),Ye(t),a=t.pendingLanes,n&4194090&&a&42?t===Of?Va++:(Va=0,Of=t):Va=0,wa(0)}}function wd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,ba(e)))}function fi(t){return Yd(),Gd(),Vd(),Xd()}function Xd(){if($t!==5)return!1;var t=En,e=Mf;Mf=0;var n=Ji(jl),l=C.T,a=Y.p;try{Y.p=32>n?32:n,C.T=null,n=Af,Af=null;var u=En,o=jl;if($t=0,ql=En=null,jl=0,_t&6)throw Error(f(331));var h=_t;if(_t|=4,Ad(u.current),Ed(u,u.current,o,n),_t=h,wa(0,!1),ie&&typeof ie.onPostCommitFiberRoot=="function")try{ie.onPostCommitFiberRoot(la,u)}catch{}return!0}finally{Y.p=a,C.T=l,wd(t,e)}}function Qd(t,e,n){e=_e(n,e),e=af(t.stateNode,e,2),t=hn(t,e,2),t!==null&&(ua(t,2),Ye(t))}function Mt(t,e,n){if(t.tag===3)Qd(t,t,n);else for(;e!==null;){if(e.tag===3){Qd(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Rn===null||!Rn.has(l))){t=_e(n,t),n=$r(2),l=hn(e,n,2),l!==null&&(kr(n,l,e,t),ua(l,2),Ye(l));break}}e=e.return}}function Cf(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new $y;var a=new Set;l.set(e,a)}else a=l.get(e),a===void 0&&(a=new Set,l.set(e,a));a.has(n)||(bf=!0,a.add(n),t=Iy.bind(null,t,e,n),e.then(t,t))}function Iy(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,At===t&&(yt&n)===n&&(Ut===4||Ut===3&&(yt&62914560)===yt&&300>ge()-Tf?!(_t&2)&&Yl(t,0):Rf|=n,Hl===yt&&(Hl=0)),Ye(t)}function Zd(t,e){e===0&&(e=Gs()),t=bl(t,e),t!==null&&(ua(t,e),Ye(t))}function tv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Zd(t,n)}function ev(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,a=t.memoizedState;a!==null&&(n=a.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(f(314))}l!==null&&l.delete(e),Zd(t,n)}function nv(t,e){return ea(t,e)}var si=null,Vl=null,Uf=!1,oi=!1,Lf=!1,tl=0;function Ye(t){t!==Vl&&t.next===null&&(Vl===null?si=Vl=t:Vl=Vl.next=t),oi=!0,Uf||(Uf=!0,av())}function wa(t,e){if(!Lf&&oi){Lf=!0;do for(var n=!1,l=si;l!==null;){if(t!==0){var a=l.pendingLanes;if(a===0)var u=0;else{var o=l.suspendedLanes,h=l.pingedLanes;u=(1<<31-ce(42|t)+1)-1,u&=a&~(o&~h),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(n=!0,kd(l,u))}else u=yt,u=vu(l,l===At?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),!(u&3)||aa(l,u)||(n=!0,kd(l,u));l=l.next}while(n);Lf=!1}}function lv(){Kd()}function Kd(){oi=Uf=!1;var t=0;tl!==0&&(dv()&&(t=tl),tl=0);for(var e=ge(),n=null,l=si;l!==null;){var a=l.next,u=Jd(l,e);u===0?(l.next=null,n===null?si=a:n.next=a,a===null&&(Vl=n)):(n=l,(t!==0||u&3)&&(oi=!0)),l=a}wa(t)}function Jd(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,a=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var o=31-ce(u),h=1<<o,S=a[o];S===-1?(!(h&n)||h&l)&&(a[o]=Dm(h,e)):S<=e&&(t.expiredLanes|=h),u&=~h}if(e=At,n=yt,n=vu(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(bt===2||bt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&ul(l),t.callbackNode=null,t.callbackPriority=0;if(!(n&3)||aa(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&ul(l),Ji(n)){case 2:case 8:n=na;break;case 32:n=Hn;break;case 268435456:n=Kt;break;default:n=Hn}return l=$d.bind(null,t),n=ea(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&ul(l),t.callbackPriority=2,t.callbackNode=null,2}function $d(t,e){if($t!==0&&$t!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(fi()&&t.callbackNode!==n)return null;var l=yt;return l=vu(t,t===At?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(xd(t,l,e),Jd(t,ge()),t.callbackNode!=null&&t.callbackNode===n?$d.bind(null,t):null)}function kd(t,e){if(fi())return null;xd(t,e,!0)}function av(){mv(function(){_t&6?ea(Bn,lv):Kd()})}function Nf(){return tl===0&&(tl=Ys()),tl}function Pd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:bu(""+t)}function Wd(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function uv(t,e,n,l,a){if(e==="submit"&&n&&n.stateNode===a){var u=Pd((a[te]||null).action),o=l.submitter;o&&(e=(e=o[te]||null)?Pd(e.formAction):o.getAttribute("formAction"),e!==null&&(u=e,o=null));var h=new Mu("action","action",null,l,a);t.push({event:h,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(tl!==0){var S=o?Wd(a,o):new FormData(a);Ic(n,{pending:!0,data:S,method:a.method,action:u},null,S)}}else typeof u=="function"&&(h.preventDefault(),S=o?Wd(a,o):new FormData(a),Ic(n,{pending:!0,data:S,method:a.method,action:u},u,S))},currentTarget:a}]})}}for(var Bf=0;Bf<pc.length;Bf++){var Hf=pc[Bf],iv=Hf.toLowerCase(),cv=Hf[0].toUpperCase()+Hf.slice(1);Ce(iv,"on"+cv)}Ce(xo,"onAnimationEnd"),Ce(Co,"onAnimationIteration"),Ce(Uo,"onAnimationStart"),Ce("dblclick","onDoubleClick"),Ce("focusin","onFocus"),Ce("focusout","onBlur"),Ce(Ty,"onTransitionRun"),Ce(My,"onTransitionStart"),Ce(Ay,"onTransitionCancel"),Ce(Lo,"onTransitionEnd"),rl("onMouseEnter",["mouseout","mouseover"]),rl("onMouseLeave",["mouseout","mouseover"]),rl("onPointerEnter",["pointerout","pointerover"]),rl("onPointerLeave",["pointerout","pointerover"]),jn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),jn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),jn("onBeforeInput",["compositionend","keypress","textInput","paste"]),jn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),jn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),jn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Xa="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),fv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Xa));function Fd(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],a=l.event;l=l.listeners;t:{var u=void 0;if(e)for(var o=l.length-1;0<=o;o--){var h=l[o],S=h.instance,z=h.currentTarget;if(h=h.listener,S!==u&&a.isPropagationStopped())break t;u=h,a.currentTarget=z;try{u(a)}catch(L){Wu(L)}a.currentTarget=null,u=S}else for(o=0;o<l.length;o++){if(h=l[o],S=h.instance,z=h.currentTarget,h=h.listener,S!==u&&a.isPropagationStopped())break t;u=h,a.currentTarget=z;try{u(a)}catch(L){Wu(L)}a.currentTarget=null,u=S}}}}function dt(t,e){var n=e[$i];n===void 0&&(n=e[$i]=new Set);var l=t+"__bubble";n.has(l)||(Id(e,t,2,!1),n.add(l))}function qf(t,e,n){var l=0;e&&(l|=4),Id(n,t,l,e)}var ri="_reactListening"+Math.random().toString(36).slice(2);function jf(t){if(!t[ri]){t[ri]=!0,Zs.forEach(function(n){n!=="selectionchange"&&(fv.has(n)||qf(n,!1,t),qf(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[ri]||(e[ri]=!0,qf("selectionchange",!1,e))}}function Id(t,e,n,l){switch(Eh(e)){case 2:var a=Bv;break;case 8:a=Hv;break;default:a=Ff}n=a.bind(null,e,n,t),a=void 0,!uc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(a=!0),l?a!==void 0?t.addEventListener(e,n,{capture:!0,passive:a}):t.addEventListener(e,n,!0):a!==void 0?t.addEventListener(e,n,{passive:a}):t.addEventListener(e,n,!1)}function Yf(t,e,n,l,a){var u=l;if(!(e&1)&&!(e&2)&&l!==null)t:for(;;){if(l===null)return;var o=l.tag;if(o===3||o===4){var h=l.stateNode.containerInfo;if(h===a)break;if(o===4)for(o=l.return;o!==null;){var S=o.tag;if((S===3||S===4)&&o.stateNode.containerInfo===a)return;o=o.return}for(;h!==null;){if(o=fl(h),o===null)return;if(S=o.tag,S===5||S===6||S===26||S===27){l=u=o;continue t}h=h.parentNode}}l=l.return}uo(function(){var z=u,L=lc(n),B=[];t:{var D=No.get(t);if(D!==void 0){var x=Mu,tt=t;switch(t){case"keypress":if(Eu(n)===0)break t;case"keydown":case"keyup":x=ny;break;case"focusin":tt="focus",x=sc;break;case"focusout":tt="blur",x=sc;break;case"beforeblur":case"afterblur":x=sc;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=fo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=Qm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=uy;break;case xo:case Co:case Uo:x=Jm;break;case Lo:x=cy;break;case"scroll":case"scrollend":x=wm;break;case"wheel":x=sy;break;case"copy":case"cut":case"paste":x=km;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=oo;break;case"toggle":case"beforetoggle":x=ry}var F=(e&4)!==0,Tt=!F&&(t==="scroll"||t==="scrollend"),T=F?D!==null?D+"Capture":null:D;F=[];for(var E=z,O;E!==null;){var N=E;if(O=N.stateNode,N=N.tag,N!==5&&N!==26&&N!==27||O===null||T===null||(N=fa(E,T),N!=null&&F.push(Qa(E,N,O))),Tt)break;E=E.return}0<F.length&&(D=new x(D,tt,null,n,L),B.push({event:D,listeners:F}))}}if(!(e&7)){t:{if(D=t==="mouseover"||t==="pointerover",x=t==="mouseout"||t==="pointerout",D&&n!==nc&&(tt=n.relatedTarget||n.fromElement)&&(fl(tt)||tt[cl]))break t;if((x||D)&&(D=L.window===L?L:(D=L.ownerDocument)?D.defaultView||D.parentWindow:window,x?(tt=n.relatedTarget||n.toElement,x=z,tt=tt?fl(tt):null,tt!==null&&(Tt=d(tt),F=tt.tag,tt!==Tt||F!==5&&F!==27&&F!==6)&&(tt=null)):(x=null,tt=z),x!==tt)){if(F=fo,N="onMouseLeave",T="onMouseEnter",E="mouse",(t==="pointerout"||t==="pointerover")&&(F=oo,N="onPointerLeave",T="onPointerEnter",E="pointer"),Tt=x==null?D:ca(x),O=tt==null?D:ca(tt),D=new F(N,E+"leave",x,n,L),D.target=Tt,D.relatedTarget=O,N=null,fl(L)===z&&(F=new F(T,E+"enter",tt,n,L),F.target=O,F.relatedTarget=Tt,N=F),Tt=N,x&&tt)e:{for(F=x,T=tt,E=0,O=F;O;O=wl(O))E++;for(O=0,N=T;N;N=wl(N))O++;for(;0<E-O;)F=wl(F),E--;for(;0<O-E;)T=wl(T),O--;for(;E--;){if(F===T||T!==null&&F===T.alternate)break e;F=wl(F),T=wl(T)}F=null}else F=null;x!==null&&th(B,D,x,F,!1),tt!==null&&Tt!==null&&th(B,Tt,tt,F,!0)}}t:{if(D=z?ca(z):window,x=D.nodeName&&D.nodeName.toLowerCase(),x==="select"||x==="input"&&D.type==="file")var K=So;else if(go(D))if(_o)K=by;else{K=Sy;var ot=py}else x=D.nodeName,!x||x.toLowerCase()!=="input"||D.type!=="checkbox"&&D.type!=="radio"?z&&ec(z.elementType)&&(K=So):K=_y;if(K&&(K=K(t,z))){po(B,K,n,L);break t}ot&&ot(t,D,z),t==="focusout"&&z&&D.type==="number"&&z.memoizedProps.value!=null&&tc(D,"number",D.value)}switch(ot=z?ca(z):window,t){case"focusin":(go(ot)||ot.contentEditable==="true")&&(pl=ot,yc=z,va=null);break;case"focusout":va=yc=pl=null;break;case"mousedown":vc=!0;break;case"contextmenu":case"mouseup":case"dragend":vc=!1,zo(B,n,L);break;case"selectionchange":if(Ey)break;case"keydown":case"keyup":zo(B,n,L)}var $;if(rc)t:{switch(t){case"compositionstart":var I="onCompositionStart";break t;case"compositionend":I="onCompositionEnd";break t;case"compositionupdate":I="onCompositionUpdate";break t}I=void 0}else gl?yo(t,n)&&(I="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(I="onCompositionStart");I&&(ro&&n.locale!=="ko"&&(gl||I!=="onCompositionStart"?I==="onCompositionEnd"&&gl&&($=io()):(sn=L,ic="value"in sn?sn.value:sn.textContent,gl=!0)),ot=di(z,I),0<ot.length&&(I=new so(I,t,null,n,L),B.push({event:I,listeners:ot}),$?I.data=$:($=vo(n),$!==null&&(I.data=$)))),($=hy?my(t,n):yy(t,n))&&(I=di(z,"onBeforeInput"),0<I.length&&(ot=new so("onBeforeInput","beforeinput",null,n,L),B.push({event:ot,listeners:I}),ot.data=$)),uv(B,t,z,n,L)}Fd(B,e)})}function Qa(t,e,n){return{instance:t,listener:e,currentTarget:n}}function di(t,e){for(var n=e+"Capture",l=[];t!==null;){var a=t,u=a.stateNode;if(a=a.tag,a!==5&&a!==26&&a!==27||u===null||(a=fa(t,n),a!=null&&l.unshift(Qa(t,a,u)),a=fa(t,e),a!=null&&l.push(Qa(t,a,u))),t.tag===3)return l;t=t.return}return[]}function wl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function th(t,e,n,l,a){for(var u=e._reactName,o=[];n!==null&&n!==l;){var h=n,S=h.alternate,z=h.stateNode;if(h=h.tag,S!==null&&S===l)break;h!==5&&h!==26&&h!==27||z===null||(S=z,a?(z=fa(n,u),z!=null&&o.unshift(Qa(n,z,S))):a||(z=fa(n,u),z!=null&&o.push(Qa(n,z,S)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var sv=/\r\n?/g,ov=/\u0000|\uFFFD/g;function eh(t){return(typeof t=="string"?t:""+t).replace(sv,`
`).replace(ov,"")}function nh(t,e){return e=eh(e),eh(t)===e}function hi(){}function Et(t,e,n,l,a,u){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||ml(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&ml(t,""+l);break;case"className":pu(t,"class",l);break;case"tabIndex":pu(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":pu(t,n,l);break;case"style":lo(t,l,u);break;case"data":if(e!=="object"){pu(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=bu(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(n==="formAction"?(e!=="input"&&Et(t,e,"name",a.name,a,null),Et(t,e,"formEncType",a.formEncType,a,null),Et(t,e,"formMethod",a.formMethod,a,null),Et(t,e,"formTarget",a.formTarget,a,null)):(Et(t,e,"encType",a.encType,a,null),Et(t,e,"method",a.method,a,null),Et(t,e,"target",a.target,a,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=bu(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=hi);break;case"onScroll":l!=null&&dt("scroll",t);break;case"onScrollEnd":l!=null&&dt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(f(61));if(n=l.__html,n!=null){if(a.children!=null)throw Error(f(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=bu(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":dt("beforetoggle",t),dt("toggle",t),gu(t,"popover",l);break;case"xlinkActuate":we(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":we(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":we(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":we(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":we(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":we(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":we(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":we(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":we(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":gu(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Gm.get(n)||n,gu(t,n,l))}}function Gf(t,e,n,l,a,u){switch(n){case"style":lo(t,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(f(61));if(n=l.__html,n!=null){if(a.children!=null)throw Error(f(60));t.innerHTML=n}}break;case"children":typeof l=="string"?ml(t,l):(typeof l=="number"||typeof l=="bigint")&&ml(t,""+l);break;case"onScroll":l!=null&&dt("scroll",t);break;case"onScrollEnd":l!=null&&dt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=hi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ks.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(a=n.endsWith("Capture"),e=n.slice(2,a?n.length-7:void 0),u=t[te]||null,u=u!=null?u[n]:null,typeof u=="function"&&t.removeEventListener(e,u,a),typeof l=="function")){typeof u!="function"&&u!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,a);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):gu(t,n,l)}}}function kt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":dt("error",t),dt("load",t);var l=!1,a=!1,u;for(u in n)if(n.hasOwnProperty(u)){var o=n[u];if(o!=null)switch(u){case"src":l=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:Et(t,e,u,o,n,null)}}a&&Et(t,e,"srcSet",n.srcSet,n,null),l&&Et(t,e,"src",n.src,n,null);return;case"input":dt("invalid",t);var h=u=o=a=null,S=null,z=null;for(l in n)if(n.hasOwnProperty(l)){var L=n[l];if(L!=null)switch(l){case"name":a=L;break;case"type":o=L;break;case"checked":S=L;break;case"defaultChecked":z=L;break;case"value":u=L;break;case"defaultValue":h=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(f(137,e));break;default:Et(t,e,l,L,n,null)}}Is(t,u,h,S,z,o,a,!1),Su(t);return;case"select":dt("invalid",t),l=o=u=null;for(a in n)if(n.hasOwnProperty(a)&&(h=n[a],h!=null))switch(a){case"value":u=h;break;case"defaultValue":o=h;break;case"multiple":l=h;default:Et(t,e,a,h,n,null)}e=u,n=o,t.multiple=!!l,e!=null?hl(t,!!l,e,!1):n!=null&&hl(t,!!l,n,!0);return;case"textarea":dt("invalid",t),u=a=l=null;for(o in n)if(n.hasOwnProperty(o)&&(h=n[o],h!=null))switch(o){case"value":l=h;break;case"defaultValue":a=h;break;case"children":u=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(f(91));break;default:Et(t,e,o,h,n,null)}eo(t,l,a,u),Su(t);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(l=n[S],l!=null))switch(S){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Et(t,e,S,l,n,null)}return;case"dialog":dt("beforetoggle",t),dt("toggle",t),dt("cancel",t),dt("close",t);break;case"iframe":case"object":dt("load",t);break;case"video":case"audio":for(l=0;l<Xa.length;l++)dt(Xa[l],t);break;case"image":dt("error",t),dt("load",t);break;case"details":dt("toggle",t);break;case"embed":case"source":case"link":dt("error",t),dt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in n)if(n.hasOwnProperty(z)&&(l=n[z],l!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:Et(t,e,z,l,n,null)}return;default:if(ec(e)){for(L in n)n.hasOwnProperty(L)&&(l=n[L],l!==void 0&&Gf(t,e,L,l,n,void 0));return}}for(h in n)n.hasOwnProperty(h)&&(l=n[h],l!=null&&Et(t,e,h,l,n,null))}function rv(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,u=null,o=null,h=null,S=null,z=null,L=null;for(x in n){var B=n[x];if(n.hasOwnProperty(x)&&B!=null)switch(x){case"checked":break;case"value":break;case"defaultValue":S=B;default:l.hasOwnProperty(x)||Et(t,e,x,null,l,B)}}for(var D in l){var x=l[D];if(B=n[D],l.hasOwnProperty(D)&&(x!=null||B!=null))switch(D){case"type":u=x;break;case"name":a=x;break;case"checked":z=x;break;case"defaultChecked":L=x;break;case"value":o=x;break;case"defaultValue":h=x;break;case"children":case"dangerouslySetInnerHTML":if(x!=null)throw Error(f(137,e));break;default:x!==B&&Et(t,e,D,x,l,B)}}Ii(t,o,h,S,z,L,u,a);return;case"select":x=o=h=D=null;for(u in n)if(S=n[u],n.hasOwnProperty(u)&&S!=null)switch(u){case"value":break;case"multiple":x=S;default:l.hasOwnProperty(u)||Et(t,e,u,null,l,S)}for(a in l)if(u=l[a],S=n[a],l.hasOwnProperty(a)&&(u!=null||S!=null))switch(a){case"value":D=u;break;case"defaultValue":h=u;break;case"multiple":o=u;default:u!==S&&Et(t,e,a,u,l,S)}e=h,n=o,l=x,D!=null?hl(t,!!n,D,!1):!!l!=!!n&&(e!=null?hl(t,!!n,e,!0):hl(t,!!n,n?[]:"",!1));return;case"textarea":x=D=null;for(h in n)if(a=n[h],n.hasOwnProperty(h)&&a!=null&&!l.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Et(t,e,h,null,l,a)}for(o in l)if(a=l[o],u=n[o],l.hasOwnProperty(o)&&(a!=null||u!=null))switch(o){case"value":D=a;break;case"defaultValue":x=a;break;case"children":break;case"dangerouslySetInnerHTML":if(a!=null)throw Error(f(91));break;default:a!==u&&Et(t,e,o,a,l,u)}to(t,D,x);return;case"option":for(var tt in n)if(D=n[tt],n.hasOwnProperty(tt)&&D!=null&&!l.hasOwnProperty(tt))switch(tt){case"selected":t.selected=!1;break;default:Et(t,e,tt,null,l,D)}for(S in l)if(D=l[S],x=n[S],l.hasOwnProperty(S)&&D!==x&&(D!=null||x!=null))switch(S){case"selected":t.selected=D&&typeof D!="function"&&typeof D!="symbol";break;default:Et(t,e,S,D,l,x)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var F in n)D=n[F],n.hasOwnProperty(F)&&D!=null&&!l.hasOwnProperty(F)&&Et(t,e,F,null,l,D);for(z in l)if(D=l[z],x=n[z],l.hasOwnProperty(z)&&D!==x&&(D!=null||x!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(f(137,e));break;default:Et(t,e,z,D,l,x)}return;default:if(ec(e)){for(var Tt in n)D=n[Tt],n.hasOwnProperty(Tt)&&D!==void 0&&!l.hasOwnProperty(Tt)&&Gf(t,e,Tt,void 0,l,D);for(L in l)D=l[L],x=n[L],!l.hasOwnProperty(L)||D===x||D===void 0&&x===void 0||Gf(t,e,L,D,l,x);return}}for(var T in n)D=n[T],n.hasOwnProperty(T)&&D!=null&&!l.hasOwnProperty(T)&&Et(t,e,T,null,l,D);for(B in l)D=l[B],x=n[B],!l.hasOwnProperty(B)||D===x||D==null&&x==null||Et(t,e,B,D,l,x)}var Vf=null,wf=null;function mi(t){return t.nodeType===9?t:t.ownerDocument}function lh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ah(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Xf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Qf=null;function dv(){var t=window.event;return t&&t.type==="popstate"?t===Qf?!1:(Qf=t,!0):(Qf=null,!1)}var uh=typeof setTimeout=="function"?setTimeout:void 0,hv=typeof clearTimeout=="function"?clearTimeout:void 0,ih=typeof Promise=="function"?Promise:void 0,mv=typeof queueMicrotask=="function"?queueMicrotask:typeof ih<"u"?function(t){return ih.resolve(null).then(t).catch(yv)}:uh;function yv(t){setTimeout(function(){throw t})}function Mn(t){return t==="head"}function ch(t,e){var n=e,l=0,a=0;do{var u=n.nextSibling;if(t.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(0<l&&8>l){n=l;var o=t.ownerDocument;if(n&1&&Za(o.documentElement),n&2&&Za(o.body),n&4)for(n=o.head,Za(n),o=n.firstChild;o;){var h=o.nextSibling,S=o.nodeName;o[ia]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&o.rel.toLowerCase()==="stylesheet"||n.removeChild(o),o=h}}if(a===0){t.removeChild(u),Ia(e);return}a--}else n==="$"||n==="$?"||n==="$!"?a++:l=n.charCodeAt(0)-48;else l=0;n=u}while(n);Ia(e)}function Zf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Zf(n),ki(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function vv(t,e,n,l){for(;t.nodeType===1;){var a=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[ia])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==a.rel||t.getAttribute("href")!==(a.href==null||a.href===""?null:a.href)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin)||t.getAttribute("title")!==(a.title==null?null:a.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(a.src==null?null:a.src)||t.getAttribute("type")!==(a.type==null?null:a.type)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=a.name==null?null:""+a.name;if(a.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Le(t.nextSibling),t===null)break}return null}function gv(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Le(t.nextSibling),t===null))return null;return t}function Kf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function pv(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Le(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Jf=null;function fh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function sh(t,e,n){switch(e=mi(n),t){case"html":if(t=e.documentElement,!t)throw Error(f(452));return t;case"head":if(t=e.head,!t)throw Error(f(453));return t;case"body":if(t=e.body,!t)throw Error(f(454));return t;default:throw Error(f(451))}}function Za(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ki(t)}var Ae=new Map,oh=new Set;function yi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var nn=Y.d;Y.d={f:Sv,r:_v,D:bv,C:Rv,L:Ev,m:Tv,X:Av,S:Mv,M:Ov};function Sv(){var t=nn.f(),e=ii();return t||e}function _v(t){var e=sl(t);e!==null&&e.tag===5&&e.type==="form"?xr(e):nn.r(t)}var Xl=typeof document>"u"?null:document;function rh(t,e,n){var l=Xl;if(l&&typeof e=="string"&&e){var a=Se(e);a='link[rel="'+t+'"][href="'+a+'"]',typeof n=="string"&&(a+='[crossorigin="'+n+'"]'),oh.has(a)||(oh.add(a),t={rel:t,crossOrigin:n,href:e},l.querySelector(a)===null&&(e=l.createElement("link"),kt(e,"link",t),Vt(e),l.head.appendChild(e)))}}function bv(t){nn.D(t),rh("dns-prefetch",t,null)}function Rv(t,e){nn.C(t,e),rh("preconnect",t,e)}function Ev(t,e,n){nn.L(t,e,n);var l=Xl;if(l&&t&&e){var a='link[rel="preload"][as="'+Se(e)+'"]';e==="image"&&n&&n.imageSrcSet?(a+='[imagesrcset="'+Se(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(a+='[imagesizes="'+Se(n.imageSizes)+'"]')):a+='[href="'+Se(t)+'"]';var u=a;switch(e){case"style":u=Ql(t);break;case"script":u=Zl(t)}Ae.has(u)||(t=b({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Ae.set(u,t),l.querySelector(a)!==null||e==="style"&&l.querySelector(Ka(u))||e==="script"&&l.querySelector(Ja(u))||(e=l.createElement("link"),kt(e,"link",t),Vt(e),l.head.appendChild(e)))}}function Tv(t,e){nn.m(t,e);var n=Xl;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",a='link[rel="modulepreload"][as="'+Se(l)+'"][href="'+Se(t)+'"]',u=a;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Zl(t)}if(!Ae.has(u)&&(t=b({rel:"modulepreload",href:t},e),Ae.set(u,t),n.querySelector(a)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ja(u)))return}l=n.createElement("link"),kt(l,"link",t),Vt(l),n.head.appendChild(l)}}}function Mv(t,e,n){nn.S(t,e,n);var l=Xl;if(l&&t){var a=ol(l).hoistableStyles,u=Ql(t);e=e||"default";var o=a.get(u);if(!o){var h={loading:0,preload:null};if(o=l.querySelector(Ka(u)))h.loading=5;else{t=b({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Ae.get(u))&&$f(t,n);var S=o=l.createElement("link");Vt(S),kt(S,"link",t),S._p=new Promise(function(z,L){S.onload=z,S.onerror=L}),S.addEventListener("load",function(){h.loading|=1}),S.addEventListener("error",function(){h.loading|=2}),h.loading|=4,vi(o,e,l)}o={type:"stylesheet",instance:o,count:1,state:h},a.set(u,o)}}}function Av(t,e){nn.X(t,e);var n=Xl;if(n&&t){var l=ol(n).hoistableScripts,a=Zl(t),u=l.get(a);u||(u=n.querySelector(Ja(a)),u||(t=b({src:t,async:!0},e),(e=Ae.get(a))&&kf(t,e),u=n.createElement("script"),Vt(u),kt(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(a,u))}}function Ov(t,e){nn.M(t,e);var n=Xl;if(n&&t){var l=ol(n).hoistableScripts,a=Zl(t),u=l.get(a);u||(u=n.querySelector(Ja(a)),u||(t=b({src:t,async:!0,type:"module"},e),(e=Ae.get(a))&&kf(t,e),u=n.createElement("script"),Vt(u),kt(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(a,u))}}function dh(t,e,n,l){var a=(a=W.current)?yi(a):null;if(!a)throw Error(f(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Ql(n.href),n=ol(a).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Ql(n.href);var u=ol(a).hoistableStyles,o=u.get(t);if(o||(a=a.ownerDocument||a,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,o),(u=a.querySelector(Ka(t)))&&!u._p&&(o.instance=u,o.state.loading=5),Ae.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ae.set(t,n),u||zv(a,t,n,o.state))),e&&l===null)throw Error(f(528,""));return o}if(e&&l!==null)throw Error(f(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Zl(n),n=ol(a).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(f(444,t))}}function Ql(t){return'href="'+Se(t)+'"'}function Ka(t){return'link[rel="stylesheet"]['+t+"]"}function hh(t){return b({},t,{"data-precedence":t.precedence,precedence:null})}function zv(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),kt(e,"link",n),Vt(e),t.head.appendChild(e))}function Zl(t){return'[src="'+Se(t)+'"]'}function Ja(t){return"script[async]"+t}function mh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Se(n.href)+'"]');if(l)return e.instance=l,Vt(l),l;var a=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Vt(l),kt(l,"style",a),vi(l,n.precedence,t),e.instance=l;case"stylesheet":a=Ql(n.href);var u=t.querySelector(Ka(a));if(u)return e.state.loading|=4,e.instance=u,Vt(u),u;l=hh(n),(a=Ae.get(a))&&$f(l,a),u=(t.ownerDocument||t).createElement("link"),Vt(u);var o=u;return o._p=new Promise(function(h,S){o.onload=h,o.onerror=S}),kt(u,"link",l),e.state.loading|=4,vi(u,n.precedence,t),e.instance=u;case"script":return u=Zl(n.src),(a=t.querySelector(Ja(u)))?(e.instance=a,Vt(a),a):(l=n,(a=Ae.get(u))&&(l=b({},n),kf(l,a)),t=t.ownerDocument||t,a=t.createElement("script"),Vt(a),kt(a,"link",l),t.head.appendChild(a),e.instance=a);case"void":return null;default:throw Error(f(443,e.type))}else e.type==="stylesheet"&&!(e.state.loading&4)&&(l=e.instance,e.state.loading|=4,vi(l,n.precedence,t));return e.instance}function vi(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=l.length?l[l.length-1]:null,u=a,o=0;o<l.length;o++){var h=l[o];if(h.dataset.precedence===e)u=h;else if(u!==a)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function $f(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function kf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var gi=null;function yh(t,e,n){if(gi===null){var l=new Map,a=gi=new Map;a.set(n,l)}else a=gi,l=a.get(n),l||(l=new Map,a.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),a=0;a<n.length;a++){var u=n[a];if(!(u[ia]||u[Pt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var o=u.getAttribute(e)||"";o=t+o;var h=l.get(o);h?h.push(u):l.set(o,[u])}}return l}function vh(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Dv(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function gh(t){return!(t.type==="stylesheet"&&!(t.state.loading&3))}var $a=null;function xv(){}function Cv(t,e,n){if($a===null)throw Error(f(475));var l=$a;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&!(e.state.loading&4)){if(e.instance===null){var a=Ql(n.href),u=t.querySelector(Ka(a));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=pi.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=u,Vt(u);return}u=t.ownerDocument||t,n=hh(n),(a=Ae.get(a))&&$f(n,a),u=u.createElement("link"),Vt(u);var o=u;o._p=new Promise(function(h,S){o.onload=h,o.onerror=S}),kt(u,"link",n),e.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&!(e.state.loading&3)&&(l.count++,e=pi.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function Uv(){if($a===null)throw Error(f(475));var t=$a;return t.stylesheets&&t.count===0&&Pf(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Pf(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function pi(){if(this.count--,this.count===0){if(this.stylesheets)Pf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Si=null;function Pf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Si=new Map,e.forEach(Lv,t),Si=null,pi.call(t))}function Lv(t,e){if(!(e.state.loading&4)){var n=Si.get(t);if(n)var l=n.get(null);else{n=new Map,Si.set(t,n);for(var a=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<a.length;u++){var o=a[u];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(n.set(o.dataset.precedence,o),l=o)}l&&n.set(null,l)}a=e.instance,o=a.getAttribute("data-precedence"),u=n.get(o)||l,u===l&&n.set(null,a),n.set(o,a),this.count++,l=pi.bind(this),a.addEventListener("load",l),a.addEventListener("error",l),u?u.parentNode.insertBefore(a,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(a,t.firstChild)),e.state.loading|=4}}var ka={$$typeof:X,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function Nv(t,e,n,l,a,u,o,h){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zi(0),this.hiddenUpdates=Zi(null),this.identifierPrefix=l,this.onUncaughtError=a,this.onCaughtError=u,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function ph(t,e,n,l,a,u,o,h,S,z,L,B){return t=new Nv(t,e,n,o,h,S,z,B),e=1,u===!0&&(e|=24),u=se(3,null,null,e),t.current=u,u.stateNode=t,e=xc(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:l,isDehydrated:n,cache:e},Nc(u),t}function Sh(t){return t?(t=Rl,t):Rl}function _h(t,e,n,l,a,u){a=Sh(a),l.context===null?l.context=a:l.pendingContext=a,l=dn(e),l.payload={element:n},u=u===void 0?null:u,u!==null&&(l.callback=u),n=hn(t,l,e),n!==null&&(me(n,t,e),Ma(n,t,e))}function bh(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Wf(t,e){bh(t,e),(t=t.alternate)&&bh(t,e)}function Rh(t){if(t.tag===13){var e=bl(t,67108864);e!==null&&me(e,t,67108864),Wf(t,67108864)}}var _i=!0;function Bv(t,e,n,l){var a=C.T;C.T=null;var u=Y.p;try{Y.p=2,Ff(t,e,n,l)}finally{Y.p=u,C.T=a}}function Hv(t,e,n,l){var a=C.T;C.T=null;var u=Y.p;try{Y.p=8,Ff(t,e,n,l)}finally{Y.p=u,C.T=a}}function Ff(t,e,n,l){if(_i){var a=If(l);if(a===null)Yf(t,e,l,bi,n),Th(t,l);else if(jv(a,t,e,n,l))l.stopPropagation();else if(Th(t,l),e&4&&-1<qv.indexOf(t)){for(;a!==null;){var u=sl(a);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var o=qn(u.pendingLanes);if(o!==0){var h=u;for(h.pendingLanes|=2,h.entangledLanes|=2;o;){var S=1<<31-ce(o);h.entanglements[1]|=S,o&=~S}Ye(u),!(_t&6)&&(ai=ge()+500,wa(0))}}break;case 13:h=bl(u,2),h!==null&&me(h,u,2),ii(),Wf(u,2)}if(u=If(l),u===null&&Yf(t,e,l,bi,n),u===a)break;a=u}a!==null&&l.stopPropagation()}else Yf(t,e,l,null,n)}}function If(t){return t=lc(t),ts(t)}var bi=null;function ts(t){if(bi=null,t=fl(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=v(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return bi=t,null}function Eh(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(il()){case Bn:return 2;case na:return 8;case Hn:case Ot:return 32;case Kt:return 268435456;default:return 32}default:return 32}}var es=!1,An=null,On=null,zn=null,Pa=new Map,Wa=new Map,Dn=[],qv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Th(t,e){switch(t){case"focusin":case"focusout":An=null;break;case"dragenter":case"dragleave":On=null;break;case"mouseover":case"mouseout":zn=null;break;case"pointerover":case"pointerout":Pa.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Wa.delete(e.pointerId)}}function Fa(t,e,n,l,a,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:u,targetContainers:[a]},e!==null&&(e=sl(e),e!==null&&Rh(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,a!==null&&e.indexOf(a)===-1&&e.push(a),t)}function jv(t,e,n,l,a){switch(e){case"focusin":return An=Fa(An,t,e,n,l,a),!0;case"dragenter":return On=Fa(On,t,e,n,l,a),!0;case"mouseover":return zn=Fa(zn,t,e,n,l,a),!0;case"pointerover":var u=a.pointerId;return Pa.set(u,Fa(Pa.get(u)||null,t,e,n,l,a)),!0;case"gotpointercapture":return u=a.pointerId,Wa.set(u,Fa(Wa.get(u)||null,t,e,n,l,a)),!0}return!1}function Mh(t){var e=fl(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=v(n),e!==null){t.blockedOn=e,Cm(t.priority,function(){if(n.tag===13){var l=he();l=Ki(l);var a=bl(n,l);a!==null&&me(a,n,l),Wf(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ri(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=If(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);nc=l,n.target.dispatchEvent(l),nc=null}else return e=sl(n),e!==null&&Rh(e),t.blockedOn=n,!1;e.shift()}return!0}function Ah(t,e,n){Ri(t)&&n.delete(e)}function Yv(){es=!1,An!==null&&Ri(An)&&(An=null),On!==null&&Ri(On)&&(On=null),zn!==null&&Ri(zn)&&(zn=null),Pa.forEach(Ah),Wa.forEach(Ah)}function Ei(t,e){t.blockedOn===e&&(t.blockedOn=null,es||(es=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Yv)))}var Ti=null;function Oh(t){Ti!==t&&(Ti=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Ti===t&&(Ti=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],a=t[e+2];if(typeof l!="function"){if(ts(l||n)===null)continue;break}var u=sl(n);u!==null&&(t.splice(e,3),e-=3,Ic(u,{pending:!0,data:a,method:n.method,action:l},l,a))}}))}function Ia(t){function e(S){return Ei(S,t)}An!==null&&Ei(An,t),On!==null&&Ei(On,t),zn!==null&&Ei(zn,t),Pa.forEach(e),Wa.forEach(e);for(var n=0;n<Dn.length;n++){var l=Dn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Dn.length&&(n=Dn[0],n.blockedOn===null);)Mh(n),n.blockedOn===null&&Dn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var a=n[l],u=n[l+1],o=a[te]||null;if(typeof u=="function")o||Oh(n);else if(o){var h=null;if(u&&u.hasAttribute("formAction")){if(a=u,o=u[te]||null)h=o.formAction;else if(ts(a)!==null)continue}else h=o.action;typeof h=="function"?n[l+1]=h:(n.splice(l,3),l-=3),Oh(n)}}}function ns(t){this._internalRoot=t}Mi.prototype.render=ns.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(f(409));var n=e.current,l=he();_h(n,l,t,e,null,null)},Mi.prototype.unmount=ns.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;_h(t.current,2,null,t,null,null),ii(),e[cl]=null}};function Mi(t){this._internalRoot=t}Mi.prototype.unstable_scheduleHydration=function(t){if(t){var e=Xs();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Dn.length&&e!==0&&e<Dn[n].priority;n++);Dn.splice(n,0,t),n===0&&Mh(t)}};var zh=s.version;if(zh!=="19.1.1")throw Error(f(527,zh,"19.1.1"));Y.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(f(188)):(t=Object.keys(t).join(","),Error(f(268,t)));return t=y(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var Gv={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:C,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ai=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ai.isDisabled&&Ai.supportsFiber)try{la=Ai.inject(Gv),ie=Ai}catch{}}return eu.createRoot=function(t,e){if(!r(t))throw Error(f(299));var n=!1,l="",a=Qr,u=Zr,o=Kr,h=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(a=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(o=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(h=e.unstable_transitionCallbacks)),e=ph(t,1,!1,null,null,n,l,a,u,o,h,null),t[cl]=e.current,jf(t),new ns(e)},eu.hydrateRoot=function(t,e,n){if(!r(t))throw Error(f(299));var l=!1,a="",u=Qr,o=Zr,h=Kr,S=null,z=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(o=n.onCaughtError),n.onRecoverableError!==void 0&&(h=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(z=n.formState)),e=ph(t,1,!0,e,n??null,l,a,u,o,h,S,z),e.context=Sh(null),n=e.current,l=he(),l=Ki(l),a=dn(l),a.callback=null,hn(n,a,l),n=l,e.current.lanes=n,ua(e,n),Ye(e),t[cl]=e.current,jf(t),new Mi(e)},eu.version="19.1.1",eu}var jh;function kv(){if(jh)return us.exports;jh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(s){console.error(s)}}return i(),us.exports=$v(),us.exports}var Pv=kv();const Wv=fm(Pv);var Fv="Invariant failed";function un(i,s){if(!i)throw new Error(Fv)}const Jl=new WeakMap,Ci=new WeakMap,Li={current:[]};let ss=!1,iu=0;const uu=new Set,Oi=new Map;function om(i){const s=Array.from(i).sort((c,f)=>c instanceof $l&&c.options.deps.includes(f)?1:f instanceof $l&&f.options.deps.includes(c)?-1:0);for(const c of s){if(Li.current.includes(c))continue;Li.current.push(c),c.recompute();const f=Ci.get(c);if(f)for(const r of f){const d=Jl.get(r);d&&om(d)}}}function Iv(i){i.listeners.forEach(s=>s({prevVal:i.prevState,currentVal:i.state}))}function tg(i){i.listeners.forEach(s=>s({prevVal:i.prevState,currentVal:i.state}))}function rm(i){if(iu>0&&!Oi.has(i)&&Oi.set(i,i.prevState),uu.add(i),!(iu>0)&&!ss)try{for(ss=!0;uu.size>0;){const s=Array.from(uu);uu.clear();for(const c of s){const f=Oi.get(c)??c.prevState;c.prevState=f,Iv(c)}for(const c of s){const f=Jl.get(c);f&&(Li.current.push(c),om(f))}for(const c of s){const f=Jl.get(c);if(f)for(const r of f)tg(r)}}}finally{ss=!1,Li.current=[],Oi.clear()}}function cu(i){iu++;try{i()}finally{if(iu--,iu===0){const s=Array.from(uu)[0];s&&rm(s)}}}function eg(i){return typeof i=="function"}class _s{constructor(s,c){this.listeners=new Set,this.subscribe=f=>{var r,d;this.listeners.add(f);const v=(d=(r=this.options)==null?void 0:r.onSubscribe)==null?void 0:d.call(r,f,this);return()=>{this.listeners.delete(f),v==null||v()}},this.prevState=s,this.state=s,this.options=c}setState(s){var c,f,r;this.prevState=this.state,(c=this.options)!=null&&c.updateFn?this.state=this.options.updateFn(this.prevState)(s):eg(s)?this.state=s(this.prevState):this.state=s,(r=(f=this.options)==null?void 0:f.onUpdate)==null||r.call(f),rm(this)}}class $l{constructor(s){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{const c=[],f=[];for(const r of this.options.deps)c.push(r.prevState),f.push(r.state);return this.lastSeenDepValues=f,{prevDepVals:c,currDepVals:f,prevVal:this.prevState??void 0}},this.recompute=()=>{var c,f;this.prevState=this.state;const{prevDepVals:r,currDepVals:d,prevVal:v}=this.getDepVals();this.state=this.options.fn({prevDepVals:r,currDepVals:d,prevVal:v}),(f=(c=this.options).onUpdate)==null||f.call(c)},this.checkIfRecalculationNeededDeeply=()=>{for(const d of this.options.deps)d instanceof $l&&d.checkIfRecalculationNeededDeeply();let c=!1;const f=this.lastSeenDepValues,{currDepVals:r}=this.getDepVals();for(let d=0;d<r.length;d++)if(r[d]!==f[d]){c=!0;break}c&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const c of this._subscriptions)c()}),this.subscribe=c=>{var f,r;this.listeners.add(c);const d=(r=(f=this.options).onSubscribe)==null?void 0:r.call(f,c,this);return()=>{this.listeners.delete(c),d==null||d()}},this.options=s,this.state=s.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(s=this.options.deps){for(const c of s)if(c instanceof $l)c.registerOnGraph(),this.registerOnGraph(c.options.deps);else if(c instanceof _s){let f=Jl.get(c);f||(f=new Set,Jl.set(c,f)),f.add(this);let r=Ci.get(this);r||(r=new Set,Ci.set(this,r)),r.add(c)}}unregisterFromGraph(s=this.options.deps){for(const c of s)if(c instanceof $l)this.unregisterFromGraph(c.options.deps);else if(c instanceof _s){const f=Jl.get(c);f&&f.delete(this);const r=Ci.get(this);r&&r.delete(c)}}}const Un="__TSR_index",Yh="popstate",Gh="beforeunload";function dm(i){let s=i.getLocation();const c=new Set,f=v=>{s=i.getLocation(),c.forEach(p=>p({location:s,action:v}))},r=v=>{i.notifyOnIndexChange??!0?f(v):s=i.getLocation()},d=async({task:v,navigateOpts:p,...y})=>{var m,b;if((p==null?void 0:p.ignoreBlocker)??!1){v();return}const _=((m=i.getBlockers)==null?void 0:m.call(i))??[],M=y.type==="PUSH"||y.type==="REPLACE";if(typeof document<"u"&&_.length&&M)for(const A of _){const U=ru(y.path,y.state);if(await A.blockerFn({currentLocation:s,nextLocation:U,action:y.type})){(b=i.onBlocked)==null||b.call(i);return}}v()};return{get location(){return s},get length(){return i.getLength()},subscribers:c,subscribe:v=>(c.add(v),()=>{c.delete(v)}),push:(v,p,y)=>{const m=s.state[Un];p=bs(m+1,p),d({task:()=>{i.pushState(v,p),f({type:"PUSH"})},navigateOpts:y,type:"PUSH",path:v,state:p})},replace:(v,p,y)=>{const m=s.state[Un];p=bs(m,p),d({task:()=>{i.replaceState(v,p),f({type:"REPLACE"})},navigateOpts:y,type:"REPLACE",path:v,state:p})},go:(v,p)=>{d({task:()=>{i.go(v),r({type:"GO",index:v})},navigateOpts:p,type:"GO"})},back:v=>{d({task:()=>{i.back((v==null?void 0:v.ignoreBlocker)??!1),r({type:"BACK"})},navigateOpts:v,type:"BACK"})},forward:v=>{d({task:()=>{i.forward((v==null?void 0:v.ignoreBlocker)??!1),r({type:"FORWARD"})},navigateOpts:v,type:"FORWARD"})},canGoBack:()=>s.state[Un]!==0,createHref:v=>i.createHref(v),block:v=>{var p;if(!i.setBlockers)return()=>{};const y=((p=i.getBlockers)==null?void 0:p.call(i))??[];return i.setBlockers([...y,v]),()=>{var m,b;const g=((m=i.getBlockers)==null?void 0:m.call(i))??[];(b=i.setBlockers)==null||b.call(i,g.filter(_=>_!==v))}},flush:()=>{var v;return(v=i.flush)==null?void 0:v.call(i)},destroy:()=>{var v;return(v=i.destroy)==null?void 0:v.call(i)},notify:f}}function bs(i,s){s||(s={});const c=Os();return{...s,key:c,__TSR_key:c,[Un]:i}}function ng(i){var s,c;const f=typeof document<"u"?window:void 0,r=f.history.pushState,d=f.history.replaceState;let v=[];const p=()=>v,y=Z=>v=Z,m=Z=>Z,b=()=>ru(`${f.location.pathname}${f.location.search}${f.location.hash}`,f.history.state);if(!((s=f.history.state)!=null&&s.__TSR_key)&&!((c=f.history.state)!=null&&c.key)){const Z=Os();f.history.replaceState({[Un]:0,key:Z,__TSR_key:Z},"")}let g=b(),_,M=!1,A=!1,U=!1,H=!1;const j=()=>g;let at,X;const J=()=>{at&&(ht._ignoreSubscribers=!0,(at.isPush?f.history.pushState:f.history.replaceState)(at.state,"",at.href),ht._ignoreSubscribers=!1,at=void 0,X=void 0,_=void 0)},V=(Z,it,mt)=>{const Gt=m(it);X||(_=g),g=ru(it,mt),at={href:Gt,state:mt,isPush:(at==null?void 0:at.isPush)||Z==="push"},X||(X=Promise.resolve().then(()=>J()))},lt=Z=>{g=b(),ht.notify({type:Z})},et=async()=>{if(A){A=!1;return}const Z=b(),it=Z.state[Un]-g.state[Un],mt=it===1,Gt=it===-1,Dt=!mt&&!Gt||M;M=!1;const Lt=Dt?"GO":Gt?"BACK":"FORWARD",C=Dt?{type:"GO",index:it}:{type:Gt?"BACK":"FORWARD"};if(U)U=!1;else{const Y=p();if(typeof document<"u"&&Y.length){for(const P of Y)if(await P.blockerFn({currentLocation:g,nextLocation:Z,action:Lt})){A=!0,f.history.go(1),ht.notify(C);return}}}g=b(),ht.notify(C)},nt=Z=>{if(H){H=!1;return}let it=!1;const mt=p();if(typeof document<"u"&&mt.length)for(const Gt of mt){const Dt=Gt.enableBeforeUnload??!0;if(Dt===!0){it=!0;break}if(typeof Dt=="function"&&Dt()===!0){it=!0;break}}if(it)return Z.preventDefault(),Z.returnValue=""},ht=dm({getLocation:j,getLength:()=>f.history.length,pushState:(Z,it)=>V("push",Z,it),replaceState:(Z,it)=>V("replace",Z,it),back:Z=>(Z&&(U=!0),H=!0,f.history.back()),forward:Z=>{Z&&(U=!0),H=!0,f.history.forward()},go:Z=>{M=!0,f.history.go(Z)},createHref:Z=>m(Z),flush:J,destroy:()=>{f.history.pushState=r,f.history.replaceState=d,f.removeEventListener(Gh,nt,{capture:!0}),f.removeEventListener(Yh,et)},onBlocked:()=>{_&&g!==_&&(g=_)},getBlockers:p,setBlockers:y,notifyOnIndexChange:!1});return f.addEventListener(Gh,nt,{capture:!0}),f.addEventListener(Yh,et),f.history.pushState=function(...Z){const it=r.apply(f.history,Z);return ht._ignoreSubscribers||lt("PUSH"),it},f.history.replaceState=function(...Z){const it=d.apply(f.history,Z);return ht._ignoreSubscribers||lt("REPLACE"),it},ht}function lg(i={initialEntries:["/"]}){const s=i.initialEntries;let c=i.initialIndex?Math.min(Math.max(i.initialIndex,0),s.length-1):s.length-1;const f=s.map((d,v)=>bs(v,void 0));return dm({getLocation:()=>ru(s[c],f[c]),getLength:()=>s.length,pushState:(d,v)=>{c<s.length-1&&(s.splice(c+1),f.splice(c+1)),f.push(v),s.push(d),c=Math.max(s.length-1,0)},replaceState:(d,v)=>{f[c]=v,s[c]=d},back:()=>{c=Math.max(c-1,0)},forward:()=>{c=Math.min(c+1,s.length-1)},go:d=>{c=Math.min(Math.max(c+d,0),s.length-1)},createHref:d=>d})}function ru(i,s){const c=i.indexOf("#"),f=i.indexOf("?"),r=Os();return{href:i,pathname:i.substring(0,c>0?f>0?Math.min(c,f):c:f>0?f:i.length),hash:c>-1?i.substring(c):"",search:f>-1?i.slice(f,c===-1?void 0:c):"",state:s||{[Un]:0,key:r,__TSR_key:r}}}function Os(){return(Math.random()+1).toString(36).substring(7)}function Rs(i){return i[i.length-1]}function ag(i){return typeof i=="function"}function el(i,s){return ag(i)?i(s):i}function Oe(i,s){if(i===s)return i;const c=s,f=Xh(i)&&Xh(c);if(f||Vh(i)&&Vh(c)){const r=f?i:Object.keys(i).concat(Object.getOwnPropertySymbols(i)),d=r.length,v=f?c:Object.keys(c).concat(Object.getOwnPropertySymbols(c)),p=v.length,y=f?[]:{};let m=0;for(let b=0;b<p;b++){const g=f?b:v[b];(!f&&r.includes(g)||f)&&i[g]===void 0&&c[g]===void 0?(y[g]=void 0,m++):(y[g]=Oe(i[g],c[g]),y[g]===i[g]&&i[g]!==void 0&&m++)}return d===p&&m===d?i:y}return c}function Vh(i){return Es(i)&&Object.getOwnPropertyNames(i).length===Object.keys(i).length}function Es(i){if(!wh(i))return!1;const s=i.constructor;if(typeof s>"u")return!0;const c=s.prototype;return!(!wh(c)||!c.hasOwnProperty("isPrototypeOf"))}function wh(i){return Object.prototype.toString.call(i)==="[object Object]"}function Xh(i){return Array.isArray(i)&&i.length===Object.keys(i).length}function Qh(i,s){let c=Object.keys(i);return s&&(c=c.filter(f=>i[f]!==void 0)),c}function kl(i,s,c){if(i===s)return!0;if(typeof i!=typeof s)return!1;if(Es(i)&&Es(s)){const f=(c==null?void 0:c.ignoreUndefined)??!0,r=Qh(i,f),d=Qh(s,f);return!(c!=null&&c.partial)&&r.length!==d.length?!1:d.every(v=>kl(i[v],s[v],c))}return Array.isArray(i)&&Array.isArray(s)?i.length!==s.length?!1:!i.some((f,r)=>!kl(f,s[r],c)):!1}function Pl(i){let s,c;const f=new Promise((r,d)=>{s=r,c=d});return f.status="pending",f.resolve=r=>{f.status="resolved",f.value=r,s(r),i==null||i(r)},f.reject=r=>{f.status="rejected",c(r)},f}function ug(i){return typeof(i==null?void 0:i.message)!="string"?!1:i.message.startsWith("Failed to fetch dynamically imported module")||i.message.startsWith("error loading dynamically imported module")||i.message.startsWith("Importing a module script failed")}function Ln(i){return!!(i&&typeof i=="object"&&typeof i.then=="function")}const ln=0,ll=1,al=2,Wl=3;function an(i){return zs(i.filter(s=>s!==void 0).join("/"))}function zs(i){return i.replace(/\/{2,}/g,"/")}function Ds(i){return i==="/"?i:i.replace(/^\/{1,}/,"")}function Fl(i){return i==="/"?i:i.replace(/\/{1,}$/,"")}function os(i){return Fl(Ds(i))}function Ni(i,s){return i!=null&&i.endsWith("/")&&i!=="/"&&i!==`${s}/`?i.slice(0,-1):i}function ig(i,s,c){return Ni(i,c)===Ni(s,c)}function cg(i){const{type:s,value:c}=i;if(s===ln)return c;const{prefixSegment:f,suffixSegment:r}=i;if(s===ll){const d=c.substring(1);if(f&&r)return`${f}{$${d}}${r}`;if(f)return`${f}{$${d}}`;if(r)return`{$${d}}${r}`}if(s===Wl){const d=c.substring(1);return f&&r?`${f}{-$${d}}${r}`:f?`${f}{-$${d}}`:r?`{-$${d}}${r}`:`{-$${d}}`}if(s===al){if(f&&r)return`${f}{$}${r}`;if(f)return`${f}{$}`;if(r)return`{$}${r}`}return c}function fg({basepath:i,base:s,to:c,trailingSlash:f="never",caseSensitive:r,parseCache:d}){var v;s=Bi(i,s,r),c=Bi(i,c,r);let p=Il(s,d).slice();const y=Il(c,d);p.length>1&&((v=Rs(p))==null?void 0:v.value)==="/"&&p.pop();for(let g=0,_=y.length;g<_;g++){const M=y[g],A=M.value;A==="/"?g?g===_-1&&p.push(M):p=[M]:A===".."?p.pop():A==="."||p.push(M)}p.length>1&&(Rs(p).value==="/"?f==="never"&&p.pop():f==="always"&&p.push({type:ln,value:"/"}));const m=p.map(cg);return an([i,...m])}const Il=(i,s)=>{if(!i)return[];const c=s==null?void 0:s.get(i);if(c)return c;const f=mg(i);return s==null||s.set(i,f),f},sg=/^\$.{1,}$/,og=/^(.*?)\{(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,rg=/^(.*?)\{-(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,dg=/^\$$/,hg=/^(.*?)\{\$\}(.*)$/;function mg(i){i=zs(i);const s=[];if(i.slice(0,1)==="/"&&(i=i.substring(1),s.push({type:ln,value:"/"})),!i)return s;const c=i.split("/").filter(Boolean);return s.push(...c.map(f=>{const r=f.match(hg);if(r){const p=r[1],y=r[2];return{type:al,value:"$",prefixSegment:p||void 0,suffixSegment:y||void 0}}const d=f.match(rg);if(d){const p=d[1],y=d[2],m=d[3];return{type:Wl,value:y,prefixSegment:p||void 0,suffixSegment:m||void 0}}const v=f.match(og);if(v){const p=v[1],y=v[2],m=v[3];return{type:ll,value:""+y,prefixSegment:p||void 0,suffixSegment:m||void 0}}if(sg.test(f)){const p=f.substring(1);return{type:ll,value:"$"+p,prefixSegment:void 0,suffixSegment:void 0}}return dg.test(f)?{type:al,value:"$",prefixSegment:void 0,suffixSegment:void 0}:{type:ln,value:f.includes("%25")?f.split("%25").map(p=>decodeURI(p)).join("%25"):decodeURI(f)}})),i.slice(-1)==="/"&&(i=i.substring(1),s.push({type:ln,value:"/"})),s}function zi({path:i,params:s,leaveWildcards:c,leaveParams:f,decodeCharMap:r,parseCache:d}){const v=Il(i,d);function p(g){const _=s[g],M=typeof _=="string";return g==="*"||g==="_splat"?M?encodeURI(_):_:M?yg(_,r):_}let y=!1;const m={},b=an(v.map(g=>{if(g.type===ln)return g.value;if(g.type===al){m._splat=s._splat;const _=g.prefixSegment||"",M=g.suffixSegment||"";if(!("_splat"in s))return y=!0,c?`${_}${g.value}${M}`:_||M?`${_}${M}`:void 0;const A=p("_splat");return c?`${_}${g.value}${A??""}${M}`:`${_}${A}${M}`}if(g.type===ll){const _=g.value.substring(1);!y&&!(_ in s)&&(y=!0),m[_]=s[_];const M=g.prefixSegment||"",A=g.suffixSegment||"";if(f){const U=p(g.value);return`${M}${g.value}${U??""}${A}`}return`${M}${p(_)??"undefined"}${A}`}if(g.type===Wl){const _=g.value.substring(1),M=g.prefixSegment||"",A=g.suffixSegment||"";if(!(_ in s)||s[_]==null)return c?`${M}${_}${A}`:M||A?`${M}${A}`:void 0;if(m[_]=s[_],f){const U=p(g.value);return`${M}${g.value}${U??""}${A}`}return c?`${M}${_}${p(_)??""}${A}`:`${M}${p(_)??""}${A}`}return g.value}));return{usedParams:m,interpolatedPath:b,isMissingParams:y}}function yg(i,s){let c=encodeURIComponent(i);if(s)for(const[f,r]of s)c=c.replaceAll(f,r);return c}function Ts(i,s,c,f){const r=vg(i,s,c,f);if(!(c.to&&!r))return r??{}}function Bi(i,s,c=!1){const f=c?i:i.toLowerCase(),r=c?s:s.toLowerCase();switch(!0){case f==="/":return s;case r===f:return"";case s.length<i.length:return s;case r[f.length]!=="/":return s;case r.startsWith(f):return s.slice(i.length);default:return s}}function vg(i,s,{to:c,fuzzy:f,caseSensitive:r},d){if(i!=="/"&&!s.startsWith(i))return;s=Bi(i,s,r),c=Bi(i,`${c??"$"}`,r);const v=Il(s.startsWith("/")?s:`/${s}`,d),p=Il(c.startsWith("/")?c:`/${c}`,d),y={};return gg(v,p,y,f,r)?y:void 0}function gg(i,s,c,f,r){var d,v,p;let y=0,m=0;for(;y<i.length||m<s.length;){const b=i[y],g=s[m];if(g){if(g.type===al){const _=i.slice(y);let M;if(g.prefixSegment||g.suffixSegment){if(!b)return!1;const A=g.prefixSegment||"",U=g.suffixSegment||"",H=b.value;if("prefixSegment"in g&&!H.startsWith(A)||"suffixSegment"in g&&!((d=i[i.length-1])!=null&&d.value.endsWith(U)))return!1;let j=decodeURI(an(_.map(at=>at.value)));A&&j.startsWith(A)&&(j=j.slice(A.length)),U&&j.endsWith(U)&&(j=j.slice(0,j.length-U.length)),M=j}else M=decodeURI(an(_.map(A=>A.value)));return c["*"]=M,c._splat=M,!0}if(g.type===ln){if(g.value==="/"&&!(b!=null&&b.value)){m++;continue}if(b){if(r){if(g.value!==b.value)return!1}else if(g.value.toLowerCase()!==b.value.toLowerCase())return!1;y++,m++;continue}else return!1}if(g.type===ll){if(!b||b.value==="/")return!1;let _="",M=!1;if(g.prefixSegment||g.suffixSegment){const A=g.prefixSegment||"",U=g.suffixSegment||"",H=b.value;if(A&&!H.startsWith(A)||U&&!H.endsWith(U))return!1;let j=H;A&&j.startsWith(A)&&(j=j.slice(A.length)),U&&j.endsWith(U)&&(j=j.slice(0,j.length-U.length)),_=decodeURIComponent(j),M=!0}else _=decodeURIComponent(b.value),M=!0;M&&(c[g.value.substring(1)]=_,y++),m++;continue}if(g.type===Wl){if(!b){m++;continue}if(b.value==="/"){m++;continue}let _="",M=!1;if(g.prefixSegment||g.suffixSegment){const A=g.prefixSegment||"",U=g.suffixSegment||"",H=b.value;if((!A||H.startsWith(A))&&(!U||H.endsWith(U))){let j=H;A&&j.startsWith(A)&&(j=j.slice(A.length)),U&&j.endsWith(U)&&(j=j.slice(0,j.length-U.length)),_=decodeURIComponent(j),M=!0}}else{let A=!0;for(let U=m+1;U<s.length;U++){const H=s[U];if((H==null?void 0:H.type)===ln&&H.value===b.value){A=!1;break}if((H==null?void 0:H.type)===ll||(H==null?void 0:H.type)===al){i.length<s.length&&(A=!1);break}}A&&(_=decodeURIComponent(b.value),M=!0)}M&&(c[g.value.substring(1)]=_,y++),m++;continue}}if(y<i.length&&m>=s.length)return c["**"]=an(i.slice(y).map(_=>_.value)),!!f&&((v=s[s.length-1])==null?void 0:v.value)!=="/";if(m<s.length&&y>=i.length){for(let _=m;_<s.length;_++)if(((p=s[_])==null?void 0:p.type)!==Wl)return!1;break}break}return!0}function Ve(i){return!!(i!=null&&i.isNotFound)}function pg(){try{if(typeof window<"u"&&typeof window.sessionStorage=="object")return window.sessionStorage}catch{}}const Hi="tsr-scroll-restoration-v1_3",Sg=(i,s)=>{let c;return(...f)=>{c||(c=setTimeout(()=>{i(...f),c=null},s))}};function _g(){const i=pg();if(!i)return;const s=i.getItem(Hi);let c=s?JSON.parse(s):{};return{state:c,set:f=>(c=el(f,c)||c,i.setItem(Hi,JSON.stringify(c)))}}const rs=_g(),Ms=i=>i.state.__TSR_key||i.href;function bg(i){const s=[];let c;for(;c=i.parentNode;)s.push(`${i.tagName}:nth-child(${Array.prototype.indexOf.call(c.children,i)+1})`),i=c;return`${s.reverse().join(" > ")}`.toLowerCase()}let qi=!1;function hm({storageKey:i,key:s,behavior:c,shouldScrollRestoration:f,scrollToTopSelectors:r,location:d}){var v,p;let y;try{y=JSON.parse(sessionStorage.getItem(i)||"{}")}catch(g){console.error(g);return}const m=s||((v=window.history.state)==null?void 0:v.key),b=y[m];qi=!0;t:{if(f&&b&&Object.keys(b).length>0){for(const M in b){const A=b[M];if(M==="window")window.scrollTo({top:A.scrollY,left:A.scrollX,behavior:c});else if(M){const U=document.querySelector(M);U&&(U.scrollLeft=A.scrollX,U.scrollTop=A.scrollY)}}break t}const g=(d??window.location).hash.split("#",2)[1];if(g){const M=((p=window.history.state)==null?void 0:p.__hashScrollIntoViewOptions)??!0;if(M){const A=document.getElementById(g);A&&A.scrollIntoView(M)}break t}const _={top:0,left:0,behavior:c};if(window.scrollTo(_),r)for(const M of r){if(M==="window")continue;const A=typeof M=="function"?M():document.querySelector(M);A&&A.scrollTo(_)}}qi=!1}function Rg(i,s){if(rs===void 0||((i.options.scrollRestoration??!1)&&(i.isScrollRestoring=!0),typeof document>"u"||i.isScrollRestorationSetup))return;i.isScrollRestorationSetup=!0,qi=!1;const f=i.options.getScrollRestorationKey||Ms;window.history.scrollRestoration="manual";const r=d=>{if(qi||!i.isScrollRestoring)return;let v="";if(d.target===document||d.target===window)v="window";else{const y=d.target.getAttribute("data-scroll-restoration-id");y?v=`[data-scroll-restoration-id="${y}"]`:v=bg(d.target)}const p=f(i.state.location);rs.set(y=>{const m=y[p]||(y[p]={}),b=m[v]||(m[v]={});if(v==="window")b.scrollX=window.scrollX||0,b.scrollY=window.scrollY||0;else if(v){const g=document.querySelector(v);g&&(b.scrollX=g.scrollLeft||0,b.scrollY=g.scrollTop||0)}return y})};typeof document<"u"&&document.addEventListener("scroll",Sg(r,100),!0),i.subscribe("onRendered",d=>{const v=f(d.toLocation);if(!i.resetNextScroll){i.resetNextScroll=!0;return}hm({storageKey:Hi,key:v,behavior:i.options.scrollRestorationBehavior,shouldScrollRestoration:i.isScrollRestoring,scrollToTopSelectors:i.options.scrollToTopSelectors,location:i.history.location}),i.isScrollRestoring&&rs.set(p=>(p[v]||(p[v]={}),p))})}function Eg(i){if(typeof document<"u"&&document.querySelector){const s=i.state.location.state.__hashScrollIntoViewOptions??!0;if(s&&i.state.location.hash!==""){const c=document.getElementById(i.state.location.hash);c&&c.scrollIntoView(s)}}}function Tg(i,s=String){const c=new URLSearchParams;for(const f in i){const r=i[f];r!==void 0&&c.set(f,s(r))}return c.toString()}function ds(i){return i?i==="false"?!1:i==="true"?!0:+i*0===0&&+i+""===i?+i:i:""}function Mg(i){const s=new URLSearchParams(i),c={};for(const[f,r]of s.entries()){const d=c[f];d==null?c[f]=ds(r):Array.isArray(d)?d.push(ds(r)):c[f]=[d,ds(r)]}return c}const Ag=zg(JSON.parse),Og=Dg(JSON.stringify,JSON.parse);function zg(i){return s=>{s[0]==="?"&&(s=s.substring(1));const c=Mg(s);for(const f in c){const r=c[f];if(typeof r=="string")try{c[f]=i(r)}catch{}}return c}}function Dg(i,s){const c=typeof s=="function";function f(r){if(typeof r=="object"&&r!==null)try{return i(r)}catch{}else if(c&&typeof r=="string")try{return s(r),i(r)}catch{}return r}return r=>{const d=Tg(r,f);return d?`?${d}`:""}}const ze="__root__";function xg(i){if(i.statusCode=i.statusCode||i.code||307,!i.reloadDocument&&typeof i.href=="string")try{new URL(i.href),i.reloadDocument=!0}catch{}const s=new Headers(i.headers||{});i.href&&s.get("Location")===null&&s.set("Location",i.href);const c=new Response(null,{status:i.statusCode,headers:s});if(c.options=i,i.throw)throw c;return c}function Ge(i){return i instanceof Response&&!!i.options}function Cg(i){const s=new Map;let c,f;const r=d=>{d.next&&(d.prev?(d.prev.next=d.next,d.next.prev=d.prev,d.next=void 0,f&&(f.next=d,d.prev=f)):(d.next.prev=void 0,c=d.next,d.next=void 0,f&&(d.prev=f,f.next=d)),f=d)};return{get(d){const v=s.get(d);if(v)return r(v),v.value},set(d,v){if(s.size>=i&&c){const y=c;s.delete(y.key),y.next&&(c=y.next,y.next.prev=void 0),y===f&&(f=void 0)}const p=s.get(d);if(p)p.value=v,r(p);else{const y={key:d,value:v,prev:f};f&&(f.next=y),f=y,c||(c=y),s.set(d,y)}}}}const Ui=i=>{var s;if(!i.rendered)return i.rendered=!0,(s=i.onReady)==null?void 0:s.call(i)},Yi=(i,s)=>!!(i.preload&&!i.router.state.matches.some(c=>c.id===s)),mm=(i,s)=>{var c;const f=i.router.routesById[s.routeId??""]??i.router.routeTree;!f.options.notFoundComponent&&((c=i.router.options)!=null&&c.defaultNotFoundComponent)&&(f.options.notFoundComponent=i.router.options.defaultNotFoundComponent),un(f.options.notFoundComponent);const r=i.matches.find(d=>d.routeId===f.id);un(r,"Could not find match for route: "+f.id),i.updateMatch(r.id,d=>({...d,status:"notFound",error:s,isFetching:!1})),s.routerCode==="BEFORE_LOAD"&&f.parentRoute&&(s.routeId=f.parentRoute.id,mm(i,s))},Cn=(i,s,c)=>{var f,r,d;if(!(!Ge(c)&&!Ve(c))){if(Ge(c)&&c.redirectHandled&&!c.options.reloadDocument)throw c;if(s){(f=s._nonReactive.beforeLoadPromise)==null||f.resolve(),(r=s._nonReactive.loaderPromise)==null||r.resolve(),s._nonReactive.beforeLoadPromise=void 0,s._nonReactive.loaderPromise=void 0;const v=Ge(c)?"redirected":"notFound";i.updateMatch(s.id,p=>({...p,status:v,isFetching:!1,error:c})),Ve(c)&&!c.routeId&&(c.routeId=s.routeId),(d=s._nonReactive.loadPromise)==null||d.resolve()}throw Ge(c)?(i.rendered=!0,c.options._fromLocation=i.location,c.redirectHandled=!0,c=i.router.resolveRedirect(c),c):(mm(i,c),c)}},ym=(i,s)=>{const c=i.router.getMatch(s);return!!(!i.router.isServer&&c._nonReactive.dehydrated||i.router.isServer&&c.ssr===!1)},nu=(i,s,c,f)=>{var r,d;const{id:v,routeId:p}=i.matches[s],y=i.router.looseRoutesById[p];if(c instanceof Promise)throw c;c.routerCode=f,i.firstBadMatchIndex??(i.firstBadMatchIndex=s),Cn(i,i.router.getMatch(v),c);try{(d=(r=y.options).onError)==null||d.call(r,c)}catch(m){c=m,Cn(i,i.router.getMatch(v),c)}i.updateMatch(v,m=>{var b,g;return(b=m._nonReactive.beforeLoadPromise)==null||b.resolve(),m._nonReactive.beforeLoadPromise=void 0,(g=m._nonReactive.loadPromise)==null||g.resolve(),{...m,error:c,status:"error",isFetching:!1,updatedAt:Date.now(),abortController:new AbortController}})},Ug=(i,s,c,f)=>{var r;const d=i.router.getMatch(s),v=(r=i.matches[c-1])==null?void 0:r.id,p=v?i.router.getMatch(v):void 0;if(i.router.isShell()){d.ssr=s===ze;return}if((p==null?void 0:p.ssr)===!1){d.ssr=!1;return}const y=A=>A===!0&&(p==null?void 0:p.ssr)==="data-only"?"data-only":A,m=i.router.options.defaultSsr??!0;if(f.options.ssr===void 0){d.ssr=y(m);return}if(typeof f.options.ssr!="function"){d.ssr=y(f.options.ssr);return}const{search:b,params:g}=d,_={search:Di(b,d.searchError),params:Di(g,d.paramsError),location:i.location,matches:i.matches.map(A=>({index:A.index,pathname:A.pathname,fullPath:A.fullPath,staticData:A.staticData,id:A.id,routeId:A.routeId,search:Di(A.search,A.searchError),params:Di(A.params,A.paramsError),ssr:A.ssr}))},M=f.options.ssr(_);if(Ln(M))return M.then(A=>{d.ssr=y(A??m)});d.ssr=y(M??m)},vm=(i,s,c,f)=>{var r;if(f._nonReactive.pendingTimeout!==void 0)return;const d=c.options.pendingMs??i.router.options.defaultPendingMs;if(!!(i.onReady&&!i.router.isServer&&!Yi(i,s)&&(c.options.loader||c.options.beforeLoad||Sm(c))&&typeof d=="number"&&d!==1/0&&(c.options.pendingComponent??((r=i.router.options)==null?void 0:r.defaultPendingComponent)))){const p=setTimeout(()=>{Ui(i)},d);f._nonReactive.pendingTimeout=p}},Lg=(i,s,c)=>{const f=i.router.getMatch(s);if(!f._nonReactive.beforeLoadPromise&&!f._nonReactive.loaderPromise)return;vm(i,s,c,f);const r=()=>{const d=i.router.getMatch(s);d.preload&&(d.status==="redirected"||d.status==="notFound")&&Cn(i,d,d.error)};return f._nonReactive.beforeLoadPromise?f._nonReactive.beforeLoadPromise.then(r):r()},Ng=(i,s,c,f)=>{var r;const d=i.router.getMatch(s),v=d._nonReactive.loadPromise;d._nonReactive.loadPromise=Pl(()=>{v==null||v.resolve()});const{paramsError:p,searchError:y}=d;p&&nu(i,c,p,"PARSE_PARAMS"),y&&nu(i,c,y,"VALIDATE_SEARCH"),vm(i,s,f,d);const m=new AbortController,b=(r=i.matches[c-1])==null?void 0:r.id,g=b?i.router.getMatch(b):void 0,M={...(g==null?void 0:g.context)??i.router.options.context??void 0,...d.__routeContext};let A=!1;const U=()=>{A||(A=!0,i.updateMatch(s,nt=>({...nt,isFetching:"beforeLoad",fetchCount:nt.fetchCount+1,abortController:m,context:M})))},H=()=>{var nt;(nt=d._nonReactive.beforeLoadPromise)==null||nt.resolve(),d._nonReactive.beforeLoadPromise=void 0,i.updateMatch(s,ht=>({...ht,isFetching:!1}))};if(!f.options.beforeLoad){cu(()=>{U(),H()});return}d._nonReactive.beforeLoadPromise=Pl();const{search:j,params:at,cause:X}=d,J=Yi(i,s),V={search:j,abortController:m,params:at,preload:J,context:M,location:i.location,navigate:nt=>i.router.navigate({...nt,_fromLocation:i.location}),buildLocation:i.router.buildLocation,cause:J?"preload":X,matches:i.matches},lt=nt=>{if(nt===void 0){cu(()=>{U(),H()});return}(Ge(nt)||Ve(nt))&&(U(),nu(i,c,nt,"BEFORE_LOAD")),cu(()=>{U(),i.updateMatch(s,ht=>({...ht,__beforeLoadContext:nt,context:{...ht.context,...nt}})),H()})};let et;try{if(et=f.options.beforeLoad(V),Ln(et))return U(),et.catch(nt=>{nu(i,c,nt,"BEFORE_LOAD")}).then(lt)}catch(nt){U(),nu(i,c,nt,"BEFORE_LOAD")}lt(et)},Bg=(i,s)=>{const{id:c,routeId:f}=i.matches[s],r=i.router.looseRoutesById[f],d=()=>{if(i.router.isServer){const y=Ug(i,c,s,r);if(Ln(y))return y.then(v)}return v()},v=()=>{if(ym(i,c))return;const y=Lg(i,c,r);return Ln(y)?y.then(p):p()},p=()=>Ng(i,c,s,r);return d()},fu=(i,s,c)=>{var f,r,d,v,p,y;const m=i.router.getMatch(s);if(!m||!c.options.head&&!c.options.scripts&&!c.options.headers)return;const b={matches:i.matches,match:m,params:m.params,loaderData:m.loaderData};return Promise.all([(r=(f=c.options).head)==null?void 0:r.call(f,b),(v=(d=c.options).scripts)==null?void 0:v.call(d,b),(y=(p=c.options).headers)==null?void 0:y.call(p,b)]).then(([g,_,M])=>{const A=g==null?void 0:g.meta,U=g==null?void 0:g.links,H=g==null?void 0:g.scripts,j=g==null?void 0:g.styles;return{meta:A,links:U,headScripts:H,headers:M,scripts:_,styles:j}})},gm=(i,s,c,f)=>{const r=i.matchPromises[c-1],{params:d,loaderDeps:v,abortController:p,context:y,cause:m}=i.router.getMatch(s),b=Yi(i,s);return{params:d,deps:v,preload:!!b,parentMatchPromise:r,abortController:p,context:y,location:i.location,navigate:g=>i.router.navigate({...g,_fromLocation:i.location}),cause:b?"preload":m,route:f}},Zh=async(i,s,c,f)=>{var r,d,v,p;try{const y=i.router.getMatch(s);try{(!i.router.isServer||y.ssr===!0)&&pm(f);const m=(d=(r=f.options).loader)==null?void 0:d.call(r,gm(i,s,c,f)),b=f.options.loader&&Ln(m);if(!!(b||f._lazyPromise||f._componentsPromise||f.options.head||f.options.scripts||f.options.headers||y._nonReactive.minPendingPromise)&&i.updateMatch(s,U=>({...U,isFetching:"loader"})),f.options.loader){const U=b?await m:m;Cn(i,i.router.getMatch(s),U),U!==void 0&&i.updateMatch(s,H=>({...H,loaderData:U}))}f._lazyPromise&&await f._lazyPromise;const _=fu(i,s,f),M=_?await _:void 0,A=y._nonReactive.minPendingPromise;A&&await A,f._componentsPromise&&await f._componentsPromise,i.updateMatch(s,U=>({...U,error:void 0,status:"success",isFetching:!1,updatedAt:Date.now(),...M}))}catch(m){let b=m;const g=y._nonReactive.minPendingPromise;g&&await g,Cn(i,i.router.getMatch(s),m);try{(p=(v=f.options).onError)==null||p.call(v,m)}catch(A){b=A,Cn(i,i.router.getMatch(s),A)}const _=fu(i,s,f),M=_?await _:void 0;i.updateMatch(s,A=>({...A,error:b,status:"error",isFetching:!1,...M}))}}catch(y){const m=i.router.getMatch(s);if(m){const b=fu(i,s,f);if(b){const g=await b;i.updateMatch(s,_=>({..._,...g}))}m._nonReactive.loaderPromise=void 0}Cn(i,m,y)}},Hg=async(i,s)=>{var c,f;const{id:r,routeId:d}=i.matches[s];let v=!1,p=!1;const y=i.router.looseRoutesById[d];if(ym(i,r)){if(i.router.isServer){const g=fu(i,r,y);if(g){const _=await g;i.updateMatch(r,M=>({...M,..._}))}return i.router.getMatch(r)}}else{const g=i.router.getMatch(r);if(g._nonReactive.loaderPromise){if(g.status==="success"&&!i.sync&&!g.preload)return g;await g._nonReactive.loaderPromise;const _=i.router.getMatch(r);_.error&&Cn(i,_,_.error)}else{const _=Date.now()-g.updatedAt,M=Yi(i,r),A=M?y.options.preloadStaleTime??i.router.options.defaultPreloadStaleTime??3e4:y.options.staleTime??i.router.options.defaultStaleTime??0,U=y.options.shouldReload,H=typeof U=="function"?U(gm(i,r,s,y)):U,j=!!M&&!i.router.state.matches.some(V=>V.id===r),at=i.router.getMatch(r);at._nonReactive.loaderPromise=Pl(),j!==at.preload&&i.updateMatch(r,V=>({...V,preload:j}));const{status:X,invalid:J}=at;if(v=X==="success"&&(J||(H??_>A)),!(M&&y.options.preload===!1))if(v&&!i.sync)p=!0,(async()=>{var V,lt;try{await Zh(i,r,s,y);const et=i.router.getMatch(r);(V=et._nonReactive.loaderPromise)==null||V.resolve(),(lt=et._nonReactive.loadPromise)==null||lt.resolve(),et._nonReactive.loaderPromise=void 0}catch(et){Ge(et)&&await i.router.navigate(et.options)}})();else if(X!=="success"||v&&i.sync)await Zh(i,r,s,y);else{const V=fu(i,r,y);if(V){const lt=await V;i.updateMatch(r,et=>({...et,...lt}))}}}}const m=i.router.getMatch(r);p||((c=m._nonReactive.loaderPromise)==null||c.resolve(),(f=m._nonReactive.loadPromise)==null||f.resolve()),clearTimeout(m._nonReactive.pendingTimeout),m._nonReactive.pendingTimeout=void 0,p||(m._nonReactive.loaderPromise=void 0),m._nonReactive.dehydrated=void 0;const b=p?m.isFetching:!1;return b!==m.isFetching||m.invalid!==!1?(i.updateMatch(r,g=>({...g,isFetching:b,invalid:!1})),i.router.getMatch(r)):m};async function Kh(i){const s=Object.assign(i,{matchPromises:[]});!s.router.isServer&&s.router.state.matches.some(c=>c._forcePending)&&Ui(s);try{for(let r=0;r<s.matches.length;r++){const d=Bg(s,r);Ln(d)&&await d}const c=s.firstBadMatchIndex??s.matches.length;for(let r=0;r<c;r++)s.matchPromises.push(Hg(s,r));await Promise.all(s.matchPromises);const f=Ui(s);Ln(f)&&await f}catch(c){if(Ve(c)&&!s.preload){const f=Ui(s);throw Ln(f)&&await f,c}if(Ge(c))throw c}return s.matches}async function pm(i){if(!i._lazyLoaded&&i._lazyPromise===void 0&&(i.lazyFn?i._lazyPromise=i.lazyFn().then(s=>{const{id:c,...f}=s.options;Object.assign(i.options,f),i._lazyLoaded=!0,i._lazyPromise=void 0}):i._lazyLoaded=!0),!i._componentsLoaded&&i._componentsPromise===void 0){const s=()=>{var c;const f=[];for(const r of _m){const d=(c=i.options[r])==null?void 0:c.preload;d&&f.push(d())}if(f.length)return Promise.all(f).then(()=>{i._componentsLoaded=!0,i._componentsPromise=void 0});i._componentsLoaded=!0,i._componentsPromise=void 0};i._componentsPromise=i._lazyPromise?i._lazyPromise.then(s):s()}return i._componentsPromise}function Di(i,s){return s?{status:"error",error:s}:{status:"success",value:i}}function Sm(i){var s;for(const c of _m)if((s=i.options[c])!=null&&s.preload)return!0;return!1}const _m=["component","errorComponent","pendingComponent","notFoundComponent"];function nl(i){const s=i.resolvedLocation,c=i.location,f=(s==null?void 0:s.pathname)!==c.pathname,r=(s==null?void 0:s.href)!==c.href,d=(s==null?void 0:s.hash)!==c.hash;return{fromLocation:s,toLocation:c,pathChanged:f,hrefChanged:r,hashChanged:d}}class qg{constructor(s){this.tempLocationKey=`${Math.round(Math.random()*1e7)}`,this.resetNextScroll=!0,this.shouldViewTransition=void 0,this.isViewTransitionTypesSupported=void 0,this.subscribers=new Set,this.isScrollRestoring=!1,this.isScrollRestorationSetup=!1,this.startTransition=c=>c(),this.update=c=>{var f;c.notFoundRoute&&console.warn("The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.");const r=this.options;this.options={...this.options,...c},this.isServer=this.options.isServer??typeof document>"u",this.pathParamsDecodeCharMap=this.options.pathParamsAllowedCharacters?new Map(this.options.pathParamsAllowedCharacters.map(d=>[encodeURIComponent(d),d])):void 0,(!this.basepath||c.basepath&&c.basepath!==r.basepath)&&(c.basepath===void 0||c.basepath===""||c.basepath==="/"?this.basepath="/":this.basepath=`/${os(c.basepath)}`),(!this.history||this.options.history&&this.options.history!==this.history)&&(this.history=this.options.history??(this.isServer?lg({initialEntries:[this.basepath||"/"]}):ng()),this.updateLatestLocation()),this.options.routeTree!==this.routeTree&&(this.routeTree=this.options.routeTree,this.buildRouteTree()),this.__store||(this.__store=new _s(Yg(this.latestLocation),{onUpdate:()=>{this.__store.state={...this.state,cachedMatches:this.state.cachedMatches.filter(d=>!["redirected"].includes(d.status))}}}),Rg(this)),typeof window<"u"&&"CSS"in window&&typeof((f=window.CSS)==null?void 0:f.supports)=="function"&&(this.isViewTransitionTypesSupported=window.CSS.supports("selector(:active-view-transition-type(a)"))},this.updateLatestLocation=()=>{this.latestLocation=this.parseLocation(this.history.location,this.latestLocation)},this.buildRouteTree=()=>{const{routesById:c,routesByPath:f,flatRoutes:r}=Kg({routeTree:this.routeTree,initRoute:(v,p)=>{v.init({originalIndex:p})}});this.routesById=c,this.routesByPath=f,this.flatRoutes=r;const d=this.options.notFoundRoute;d&&(d.init({originalIndex:99999999999}),this.routesById[d.id]=d)},this.subscribe=(c,f)=>{const r={eventType:c,fn:f};return this.subscribers.add(r),()=>{this.subscribers.delete(r)}},this.emit=c=>{this.subscribers.forEach(f=>{f.eventType===c.type&&f.fn(c)})},this.parseLocation=(c,f)=>{const r=({pathname:y,search:m,hash:b,state:g})=>{const _=this.options.parseSearch(m),M=this.options.stringifySearch(_);return{pathname:y,searchStr:M,search:Oe(f==null?void 0:f.search,_),hash:b.split("#").reverse()[0]??"",href:`${y}${M}${b}`,state:Oe(f==null?void 0:f.state,g)}},d=r(c),{__tempLocation:v,__tempKey:p}=d.state;if(v&&(!p||p===this.tempLocationKey)){const y=r(v);return y.state.key=d.state.key,y.state.__TSR_key=d.state.__TSR_key,delete y.state.__tempLocation,{...y,maskedLocation:d}}return d},this.resolvePathWithBase=(c,f)=>fg({basepath:this.basepath,base:c,to:zs(f),trailingSlash:this.options.trailingSlash,caseSensitive:this.options.caseSensitive,parseCache:this.parsePathnameCache}),this.matchRoutes=(c,f,r)=>typeof c=="string"?this.matchRoutesInternal({pathname:c,search:f},r):this.matchRoutesInternal(c,f),this.parsePathnameCache=Cg(1e3),this.getMatchedRoutes=(c,f)=>Jg({pathname:c,routePathname:f,basepath:this.basepath,caseSensitive:this.options.caseSensitive,routesByPath:this.routesByPath,routesById:this.routesById,flatRoutes:this.flatRoutes,parseCache:this.parsePathnameCache}),this.cancelMatch=c=>{const f=this.getMatch(c);f&&(f.abortController.abort(),clearTimeout(f._nonReactive.pendingTimeout),f._nonReactive.pendingTimeout=void 0)},this.cancelMatches=()=>{var c;(c=this.state.pendingMatches)==null||c.forEach(f=>{this.cancelMatch(f.id)})},this.buildLocation=c=>{const f=(d={})=>{var v,p;const y=d._fromLocation||this.latestLocation,m=this.matchRoutes(y,{_buildLocation:!0}),b=Rs(m);let g=this.resolvePathWithBase(b.fullPath,".");const _=d.to?this.resolvePathWithBase(g,`${d.to}`):this.resolvePathWithBase(g,"."),M=!!d.to&&!$h(d.to.toString(),g)&&!$h(_,g);d.unsafeRelative==="path"?g=y.pathname:M&&d.from&&(g=d.from),g=this.resolvePathWithBase(g,".");const A=b.search,U={...b.params},H=d.to?this.resolvePathWithBase(g,`${d.to}`):this.resolvePathWithBase(g,"."),j=d.params===!1||d.params===null?{}:(d.params??!0)===!0?U:Object.assign(U,el(d.params,U)),at=zi({path:H,params:j,parseCache:this.parsePathnameCache}).interpolatedPath,X=this.matchRoutes(at,void 0,{_buildLocation:!0}).map(Z=>this.looseRoutesById[Z.routeId]);if(Object.keys(j).length>0)for(const Z of X){const it=((v=Z.options.params)==null?void 0:v.stringify)??Z.options.stringifyParams;it&&Object.assign(j,it(j))}const J=zi({path:H,params:j,leaveWildcards:!1,leaveParams:c.leaveParams,decodeCharMap:this.pathParamsDecodeCharMap,parseCache:this.parsePathnameCache}).interpolatedPath;let V=A;if(c._includeValidateSearch&&((p=this.options.search)!=null&&p.strict)){const Z={};X.forEach(it=>{if(it.options.validateSearch)try{Object.assign(Z,As(it.options.validateSearch,{...Z,...V}))}catch{}}),V=Z}V=$g({search:V,dest:d,destRoutes:X,_includeValidateSearch:c._includeValidateSearch}),V=Oe(A,V);const lt=this.options.stringifySearch(V),et=d.hash===!0?y.hash:d.hash?el(d.hash,y.hash):void 0,nt=et?`#${et}`:"";let ht=d.state===!0?y.state:d.state?el(d.state,y.state):{};return ht=Oe(y.state,ht),{pathname:J,search:V,searchStr:lt,state:ht,hash:et??"",href:`${J}${lt}${nt}`,unmaskOnReload:d.unmaskOnReload}},r=(d={},v)=>{var p;const y=f(d);let m=v?f(v):void 0;if(!m){let b={};const g=(p=this.options.routeMasks)==null?void 0:p.find(_=>{const M=Ts(this.basepath,y.pathname,{to:_.from,caseSensitive:!1,fuzzy:!1},this.parsePathnameCache);return M?(b=M,!0):!1});if(g){const{from:_,...M}=g;v={from:c.from,...M,params:b},m=f(v)}}if(m){const b=f(v);y.maskedLocation=b}return y};return c.mask?r(c,{from:c.from,...c.mask}):r(c)},this.commitLocation=({viewTransition:c,ignoreBlocker:f,...r})=>{const d=()=>{const y=["key","__TSR_key","__TSR_index","__hashScrollIntoViewOptions"];y.forEach(b=>{r.state[b]=this.latestLocation.state[b]});const m=kl(r.state,this.latestLocation.state);return y.forEach(b=>{delete r.state[b]}),m},v=this.latestLocation.href===r.href,p=this.commitLocationPromise;if(this.commitLocationPromise=Pl(()=>{p==null||p.resolve()}),v&&d())this.load();else{let{maskedLocation:y,hashScrollIntoView:m,...b}=r;y&&(b={...y,state:{...y.state,__tempKey:void 0,__tempLocation:{...b,search:b.searchStr,state:{...b.state,__tempKey:void 0,__tempLocation:void 0,__TSR_key:void 0,key:void 0}}}},(b.unmaskOnReload??this.options.unmaskOnReload??!1)&&(b.state.__tempKey=this.tempLocationKey)),b.state.__hashScrollIntoViewOptions=m??this.options.defaultHashScrollIntoView??!0,this.shouldViewTransition=c,this.history[r.replace?"replace":"push"](b.href,b.state,{ignoreBlocker:f})}return this.resetNextScroll=r.resetScroll??!0,this.history.subscribers.size||this.load(),this.commitLocationPromise},this.buildAndCommitLocation=({replace:c,resetScroll:f,hashScrollIntoView:r,viewTransition:d,ignoreBlocker:v,href:p,...y}={})=>{if(p){const b=this.history.location.state.__TSR_index,g=ru(p,{__TSR_index:c?b:b+1});y.to=g.pathname,y.search=this.options.parseSearch(g.search),y.hash=g.hash.slice(1)}const m=this.buildLocation({...y,_includeValidateSearch:!0});return this.commitLocation({...m,viewTransition:d,replace:c,resetScroll:f,hashScrollIntoView:r,ignoreBlocker:v})},this.navigate=({to:c,reloadDocument:f,href:r,...d})=>{if(!f&&r)try{new URL(`${r}`),f=!0}catch{}if(f){if(!r){const v=this.buildLocation({to:c,...d});r=this.history.createHref(v.href)}return d.replace?window.location.replace(r):window.location.href=r,Promise.resolve()}return this.buildAndCommitLocation({...d,href:r,to:c,_isNavigate:!0})},this.beforeLoad=()=>{if(this.cancelMatches(),this.updateLatestLocation(),this.isServer){const f=this.buildLocation({to:this.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0}),r=d=>{try{return encodeURI(decodeURI(d))}catch{return d}};if(os(r(this.latestLocation.href))!==os(r(f.href)))throw xg({href:f.href})}const c=this.matchRoutes(this.latestLocation);this.__store.setState(f=>({...f,status:"pending",statusCode:200,isLoading:!0,location:this.latestLocation,pendingMatches:c,cachedMatches:f.cachedMatches.filter(r=>!c.some(d=>d.id===r.id))}))},this.load=async c=>{let f,r,d;for(d=new Promise(v=>{this.startTransition(async()=>{var p;try{this.beforeLoad();const y=this.latestLocation,m=this.state.resolvedLocation;this.state.redirect||this.emit({type:"onBeforeNavigate",...nl({resolvedLocation:m,location:y})}),this.emit({type:"onBeforeLoad",...nl({resolvedLocation:m,location:y})}),await Kh({router:this,sync:c==null?void 0:c.sync,matches:this.state.pendingMatches,location:y,updateMatch:this.updateMatch,onReady:async()=>{this.startViewTransition(async()=>{let b,g,_;cu(()=>{this.__store.setState(M=>{const A=M.matches,U=M.pendingMatches||M.matches;return b=A.filter(H=>!U.some(j=>j.id===H.id)),g=U.filter(H=>!A.some(j=>j.id===H.id)),_=A.filter(H=>U.some(j=>j.id===H.id)),{...M,isLoading:!1,loadedAt:Date.now(),matches:U,pendingMatches:void 0,cachedMatches:[...M.cachedMatches,...b.filter(H=>H.status!=="error")]}}),this.clearExpiredCache()}),[[b,"onLeave"],[g,"onEnter"],[_,"onStay"]].forEach(([M,A])=>{M.forEach(U=>{var H,j;(j=(H=this.looseRoutesById[U.routeId].options)[A])==null||j.call(H,U)})})})}})}catch(y){Ge(y)?(f=y,this.isServer||this.navigate({...f.options,replace:!0,ignoreBlocker:!0})):Ve(y)&&(r=y),this.__store.setState(m=>({...m,statusCode:f?f.status:r?404:m.matches.some(b=>b.status==="error")?500:200,redirect:f}))}this.latestLoadPromise===d&&((p=this.commitLocationPromise)==null||p.resolve(),this.latestLoadPromise=void 0,this.commitLocationPromise=void 0),v()})}),this.latestLoadPromise=d,await d;this.latestLoadPromise&&d!==this.latestLoadPromise;)await this.latestLoadPromise;this.hasNotFoundMatch()&&this.__store.setState(v=>({...v,statusCode:404}))},this.startViewTransition=c=>{const f=this.shouldViewTransition??this.options.defaultViewTransition;if(delete this.shouldViewTransition,f&&typeof document<"u"&&"startViewTransition"in document&&typeof document.startViewTransition=="function"){let r;if(typeof f=="object"&&this.isViewTransitionTypesSupported){const d=this.latestLocation,v=this.state.resolvedLocation,p=typeof f.types=="function"?f.types(nl({resolvedLocation:v,location:d})):f.types;r={update:c,types:p}}else r=c;document.startViewTransition(r)}else c()},this.updateMatch=(c,f)=>{var r;const d=(r=this.state.pendingMatches)!=null&&r.some(v=>v.id===c)?"pendingMatches":this.state.matches.some(v=>v.id===c)?"matches":this.state.cachedMatches.some(v=>v.id===c)?"cachedMatches":"";d&&this.__store.setState(v=>{var p;return{...v,[d]:(p=v[d])==null?void 0:p.map(y=>y.id===c?f(y):y)}})},this.getMatch=c=>{var f;const r=d=>d.id===c;return this.state.cachedMatches.find(r)??((f=this.state.pendingMatches)==null?void 0:f.find(r))??this.state.matches.find(r)},this.invalidate=c=>{const f=r=>{var d;return((d=c==null?void 0:c.filter)==null?void 0:d.call(c,r))??!0?{...r,invalid:!0,...c!=null&&c.forcePending||r.status==="error"?{status:"pending",error:void 0}:void 0}:r};return this.__store.setState(r=>{var d;return{...r,matches:r.matches.map(f),cachedMatches:r.cachedMatches.map(f),pendingMatches:(d=r.pendingMatches)==null?void 0:d.map(f)}}),this.shouldViewTransition=!1,this.load({sync:c==null?void 0:c.sync})},this.resolveRedirect=c=>(c.options.href||(c.options.href=this.buildLocation(c.options).href,c.headers.set("Location",c.options.href)),c.headers.get("Location")||c.headers.set("Location",c.options.href),c),this.clearCache=c=>{const f=c==null?void 0:c.filter;f!==void 0?this.__store.setState(r=>({...r,cachedMatches:r.cachedMatches.filter(d=>!f(d))})):this.__store.setState(r=>({...r,cachedMatches:[]}))},this.clearExpiredCache=()=>{const c=f=>{const r=this.looseRoutesById[f.routeId];if(!r.options.loader)return!0;const d=(f.preload?r.options.preloadGcTime??this.options.defaultPreloadGcTime:r.options.gcTime??this.options.defaultGcTime)??5*60*1e3;return f.status==="error"?!0:Date.now()-f.updatedAt>=d};this.clearCache({filter:c})},this.loadRouteChunk=pm,this.preloadRoute=async c=>{const f=this.buildLocation(c);let r=this.matchRoutes(f,{throwOnError:!0,preload:!0,dest:c});const d=new Set([...this.state.matches,...this.state.pendingMatches??[]].map(p=>p.id)),v=new Set([...d,...this.state.cachedMatches.map(p=>p.id)]);cu(()=>{r.forEach(p=>{v.has(p.id)||this.__store.setState(y=>({...y,cachedMatches:[...y.cachedMatches,p]}))})});try{return r=await Kh({router:this,matches:r,location:f,preload:!0,updateMatch:(p,y)=>{d.has(p)?r=r.map(m=>m.id===p?y(m):m):this.updateMatch(p,y)}}),r}catch(p){if(Ge(p))return p.options.reloadDocument?void 0:await this.preloadRoute({...p.options,_fromLocation:f});Ve(p)||console.error(p);return}},this.matchRoute=(c,f)=>{const r={...c,to:c.to?this.resolvePathWithBase(c.from||"",c.to):void 0,params:c.params||{},leaveParams:!0},d=this.buildLocation(r);if(f!=null&&f.pending&&this.state.status!=="pending")return!1;const p=((f==null?void 0:f.pending)===void 0?!this.state.isLoading:f.pending)?this.latestLocation:this.state.resolvedLocation||this.state.location,y=Ts(this.basepath,p.pathname,{...f,to:d.pathname},this.parsePathnameCache);return!y||c.params&&!kl(y,c.params,{partial:!0})?!1:y&&((f==null?void 0:f.includeSearch)??!0)?kl(p.search,d.search,{partial:!0})?y:!1:y},this.hasNotFoundMatch=()=>this.__store.state.matches.some(c=>c.status==="notFound"||c.globalNotFound),this.update({defaultPreloadDelay:50,defaultPendingMs:1e3,defaultPendingMinMs:500,context:void 0,...s,caseSensitive:s.caseSensitive??!1,notFoundMode:s.notFoundMode??"fuzzy",stringifySearch:s.stringifySearch??Og,parseSearch:s.parseSearch??Ag}),typeof document<"u"&&(self.__TSR_ROUTER__=this)}isShell(){return!!this.options.isShell}isPrerendering(){return!!this.options.isPrerendering}get state(){return this.__store.state}get looseRoutesById(){return this.routesById}matchRoutesInternal(s,c){var f;const{foundRoute:r,matchedRoutes:d,routeParams:v}=this.getMatchedRoutes(s.pathname,(f=c==null?void 0:c.dest)==null?void 0:f.to);let p=!1;(r?r.path!=="/"&&v["**"]:Fl(s.pathname))&&(this.options.notFoundRoute?d.push(this.options.notFoundRoute):p=!0);const y=(()=>{if(p){if(this.options.notFoundMode!=="root")for(let _=d.length-1;_>=0;_--){const M=d[_];if(M.children)return M.id}return ze}})(),m=d.map(_=>{var M;let A;const U=((M=_.options.params)==null?void 0:M.parse)??_.options.parseParams;if(U)try{const H=U(v);Object.assign(v,H)}catch(H){if(A=new jg(H.message,{cause:H}),c!=null&&c.throwOnError)throw A;return A}}),b=[],g=_=>(_==null?void 0:_.id)?_.context??this.options.context??void 0:this.options.context??void 0;return d.forEach((_,M)=>{var A,U;const H=b[M-1],[j,at,X]=(()=>{const Dt=(H==null?void 0:H.search)??s.search,Lt=(H==null?void 0:H._strictSearch)??void 0;try{const C=As(_.options.validateSearch,{...Dt})??void 0;return[{...Dt,...C},{...Lt,...C},void 0]}catch(C){let Y=C;if(C instanceof ji||(Y=new ji(C.message,{cause:C})),c!=null&&c.throwOnError)throw Y;return[Dt,{},Y]}})(),J=((U=(A=_.options).loaderDeps)==null?void 0:U.call(A,{search:j}))??"",V=J?JSON.stringify(J):"",{usedParams:lt,interpolatedPath:et}=zi({path:_.fullPath,params:v,decodeCharMap:this.pathParamsDecodeCharMap}),nt=zi({path:_.id,params:v,leaveWildcards:!0,decodeCharMap:this.pathParamsDecodeCharMap,parseCache:this.parsePathnameCache}).interpolatedPath+V,ht=this.getMatch(nt),Z=this.state.matches.find(Dt=>Dt.routeId===_.id),it=Z?"stay":"enter";let mt;if(ht)mt={...ht,cause:it,params:Z?Oe(Z.params,v):v,_strictParams:lt,search:Oe(Z?Z.search:ht.search,j),_strictSearch:at};else{const Dt=_.options.loader||_.options.beforeLoad||_.lazyFn||Sm(_)?"pending":"success";mt={id:nt,index:M,routeId:_.id,params:Z?Oe(Z.params,v):v,_strictParams:lt,pathname:an([this.basepath,et]),updatedAt:Date.now(),search:Z?Oe(Z.search,j):j,_strictSearch:at,searchError:void 0,status:Dt,isFetching:!1,error:void 0,paramsError:m[M],__routeContext:void 0,_nonReactive:{loadPromise:Pl()},__beforeLoadContext:void 0,context:{},abortController:new AbortController,fetchCount:0,cause:it,loaderDeps:Z?Oe(Z.loaderDeps,J):J,invalid:!1,preload:!1,links:void 0,scripts:void 0,headScripts:void 0,meta:void 0,staticData:_.options.staticData||{},fullPath:_.fullPath}}c!=null&&c.preload||(mt.globalNotFound=y===_.id),mt.searchError=X;const Gt=g(H);mt.context={...Gt,...mt.__routeContext,...mt.__beforeLoadContext},b.push(mt)}),b.forEach((_,M)=>{const A=this.looseRoutesById[_.routeId];if(!this.getMatch(_.id)&&(c==null?void 0:c._buildLocation)!==!0){const H=b[M-1],j=g(H);if(A.options.context){const at={deps:_.loaderDeps,params:_.params,context:j??{},location:s,navigate:X=>this.navigate({...X,_fromLocation:s}),buildLocation:this.buildLocation,cause:_.cause,abortController:_.abortController,preload:!!_.preload,matches:b};_.__routeContext=A.options.context(at)??void 0}_.context={...j,..._.__routeContext,..._.__beforeLoadContext}}}),b}}class ji extends Error{}class jg extends Error{}const Jh=i=>i.endsWith("/")&&i.length>1?i.slice(0,-1):i;function $h(i,s){return Jh(i)===Jh(s)}function Yg(i){return{loadedAt:0,isLoading:!1,isTransitioning:!1,status:"idle",resolvedLocation:void 0,location:i,matches:[],pendingMatches:[],cachedMatches:[],statusCode:200}}function As(i,s){if(i==null)return{};if("~standard"in i){const c=i["~standard"].validate(s);if(c instanceof Promise)throw new ji("Async validation not supported");if(c.issues)throw new ji(JSON.stringify(c.issues,void 0,2),{cause:c});return c.value}return"parse"in i?i.parse(s):typeof i=="function"?i(s):{}}const Gg=.5,Vg=.4,wg=.25,Xg=.05,Qg=.02,Zg=.01,kh=2e-4,Ph=1e-4;function Wh(i,s){return i.prefixSegment&&i.suffixSegment?s+Xg+kh*i.prefixSegment.length+Ph*i.suffixSegment.length:i.prefixSegment?s+Qg+kh*i.prefixSegment.length:i.suffixSegment?s+Zg+Ph*i.suffixSegment.length:s}function Kg({routeTree:i,initRoute:s}){const c={},f={},r=y=>{y.forEach((m,b)=>{s==null||s(m,b);const g=c[m.id];if(un(!g,`Duplicate routes found with id: ${String(m.id)}`),c[m.id]=m,!m.isRoot&&m.path){const M=Fl(m.fullPath);(!f[M]||m.fullPath.endsWith("/"))&&(f[M]=m)}const _=m.children;_!=null&&_.length&&r(_)})};r([i]);const d=[];Object.values(c).forEach((y,m)=>{var b;if(y.isRoot||!y.path)return;const g=Ds(y.fullPath);let _=Il(g),M=0;for(;_.length>M+1&&((b=_[M])==null?void 0:b.value)==="/";)M++;M>0&&(_=_.slice(M));let A=0,U=!1;const H=_.map((j,at)=>{if(j.value==="/")return .75;let X;if(j.type===ll?X=Gg:j.type===Wl?(X=Vg,A++):j.type===al&&(X=wg),X){for(let J=at+1;J<_.length;J++){const V=_[J];if(V.type===ln&&V.value!=="/")return U=!0,Wh(j,X+.2)}return Wh(j,X)}return 1});d.push({child:y,trimmed:g,parsed:_,index:m,scores:H,optionalParamCount:A,hasStaticAfter:U})});const p=d.sort((y,m)=>{const b=Math.min(y.scores.length,m.scores.length);for(let g=0;g<b;g++)if(y.scores[g]!==m.scores[g])return m.scores[g]-y.scores[g];if(y.scores.length!==m.scores.length){if(y.optionalParamCount!==m.optionalParamCount){if(y.hasStaticAfter===m.hasStaticAfter)return y.optionalParamCount-m.optionalParamCount;if(y.hasStaticAfter&&!m.hasStaticAfter)return-1;if(!y.hasStaticAfter&&m.hasStaticAfter)return 1}return m.scores.length-y.scores.length}for(let g=0;g<b;g++)if(y.parsed[g].value!==m.parsed[g].value)return y.parsed[g].value>m.parsed[g].value?1:-1;return y.index-m.index}).map((y,m)=>(y.child.rank=m,y.child));return{routesById:c,routesByPath:f,flatRoutes:p}}function Jg({pathname:i,routePathname:s,basepath:c,caseSensitive:f,routesByPath:r,routesById:d,flatRoutes:v,parseCache:p}){let y={};const m=Fl(i),b=A=>{var U;return Ts(c,m,{to:A.fullPath,caseSensitive:((U=A.options)==null?void 0:U.caseSensitive)??f,fuzzy:!0},p)};let g=s!==void 0?r[s]:void 0;if(g)y=b(g);else{let A;for(const U of v){const H=b(U);if(H)if(U.path!=="/"&&H["**"])A||(A={foundRoute:U,routeParams:H});else{g=U,y=H;break}}!g&&A&&(g=A.foundRoute,y=A.routeParams)}let _=g||d[ze];const M=[_];for(;_.parentRoute;)_=_.parentRoute,M.push(_);return M.reverse(),{matchedRoutes:M,routeParams:y,foundRoute:g}}function $g({search:i,dest:s,destRoutes:c,_includeValidateSearch:f}){const r=c.reduce((p,y)=>{var m;const b=[];if("search"in y.options)(m=y.options.search)!=null&&m.middlewares&&b.push(...y.options.search.middlewares);else if(y.options.preSearchFilters||y.options.postSearchFilters){const g=({search:_,next:M})=>{let A=_;"preSearchFilters"in y.options&&y.options.preSearchFilters&&(A=y.options.preSearchFilters.reduce((H,j)=>j(H),_));const U=M(A);return"postSearchFilters"in y.options&&y.options.postSearchFilters?y.options.postSearchFilters.reduce((H,j)=>j(H),U):U};b.push(g)}if(f&&y.options.validateSearch){const g=({search:_,next:M})=>{const A=M(_);try{return{...A,...As(y.options.validateSearch,A)??void 0}}catch{return A}};b.push(g)}return p.concat(b)},[])??[],d=({search:p})=>s.search?s.search===!0?p:el(s.search,p):{};r.push(d);const v=(p,y)=>{if(p>=r.length)return y;const m=r[p];return m({search:y,next:g=>v(p+1,g)})};return v(0,i)}const kg="Error preloading route! ☝️";class bm{constructor(s){if(this.init=c=>{var f,r;this.originalIndex=c.originalIndex;const d=this.options,v=!(d!=null&&d.path)&&!(d!=null&&d.id);this.parentRoute=(r=(f=this.options).getParentRoute)==null?void 0:r.call(f),v?this._path=ze:this.parentRoute||un(!1);let p=v?ze:d==null?void 0:d.path;p&&p!=="/"&&(p=Ds(p));const y=(d==null?void 0:d.id)||p;let m=v?ze:an([this.parentRoute.id===ze?"":this.parentRoute.id,y]);p===ze&&(p="/"),m!==ze&&(m=an(["/",m]));const b=m===ze?"/":an([this.parentRoute.fullPath,p]);this._path=p,this._id=m,this._fullPath=b,this._to=b},this.clone=c=>{this._path=c._path,this._id=c._id,this._fullPath=c._fullPath,this._to=c._to,this.options.getParentRoute=c.options.getParentRoute,this.children=c.children},this.addChildren=c=>this._addFileChildren(c),this._addFileChildren=c=>(Array.isArray(c)&&(this.children=c),typeof c=="object"&&c!==null&&(this.children=Object.values(c)),this),this._addFileTypes=()=>this,this.updateLoader=c=>(Object.assign(this.options,c),this),this.update=c=>(Object.assign(this.options,c),this),this.lazy=c=>(this.lazyFn=c,this),this.options=s||{},this.isRoot=!(s!=null&&s.getParentRoute),s!=null&&s.id&&(s!=null&&s.path))throw new Error("Route cannot have both an 'id' and a 'path' option.")}get to(){return this._to}get id(){return this._id}get path(){return this._path}get fullPath(){return this._fullPath}}class Pg extends bm{constructor(s){super(s)}}function xs(i){const s=i.errorComponent??Gi;return Q.jsx(Wg,{getResetKey:i.getResetKey,onCatch:i.onCatch,children:({error:c,reset:f})=>c?ut.createElement(s,{error:c,reset:f}):i.children})}class Wg extends ut.Component{constructor(){super(...arguments),this.state={error:null}}static getDerivedStateFromProps(s){return{resetKey:s.getResetKey()}}static getDerivedStateFromError(s){return{error:s}}reset(){this.setState({error:null})}componentDidUpdate(s,c){c.error&&c.resetKey!==this.state.resetKey&&this.reset()}componentDidCatch(s,c){this.props.onCatch&&this.props.onCatch(s,c)}render(){return this.props.children({error:this.state.resetKey!==this.props.getResetKey()?null:this.state.error,reset:()=>{this.reset()}})}}function Gi({error:i}){const[s,c]=ut.useState(!1);return Q.jsxs("div",{style:{padding:".5rem",maxWidth:"100%"},children:[Q.jsxs("div",{style:{display:"flex",alignItems:"center",gap:".5rem"},children:[Q.jsx("strong",{style:{fontSize:"1rem"},children:"Something went wrong!"}),Q.jsx("button",{style:{appearance:"none",fontSize:".6em",border:"1px solid currentColor",padding:".1rem .2rem",fontWeight:"bold",borderRadius:".25rem"},onClick:()=>c(f=>!f),children:s?"Hide Error":"Show Error"})]}),Q.jsx("div",{style:{height:".25rem"}}),s?Q.jsx("div",{children:Q.jsx("pre",{style:{fontSize:".7em",border:"1px solid red",borderRadius:".25rem",padding:".3rem",color:"red",overflow:"auto"},children:i.message?Q.jsx("code",{children:i.message}):null})}):null]})}function Fg({children:i,fallback:s=null}){return Ig()?Q.jsx(ou.Fragment,{children:i}):Q.jsx(ou.Fragment,{children:s})}function Ig(){return ou.useSyncExternalStore(tp,()=>!0,()=>!1)}function tp(){return()=>{}}var hs={exports:{}},ms={},ys={exports:{}},vs={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fh;function ep(){if(Fh)return vs;Fh=1;var i=hu();function s(g,_){return g===_&&(g!==0||1/g===1/_)||g!==g&&_!==_}var c=typeof Object.is=="function"?Object.is:s,f=i.useState,r=i.useEffect,d=i.useLayoutEffect,v=i.useDebugValue;function p(g,_){var M=_(),A=f({inst:{value:M,getSnapshot:_}}),U=A[0].inst,H=A[1];return d(function(){U.value=M,U.getSnapshot=_,y(U)&&H({inst:U})},[g,M,_]),r(function(){return y(U)&&H({inst:U}),g(function(){y(U)&&H({inst:U})})},[g]),v(M),M}function y(g){var _=g.getSnapshot;g=g.value;try{var M=_();return!c(g,M)}catch{return!0}}function m(g,_){return _()}var b=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?m:p;return vs.useSyncExternalStore=i.useSyncExternalStore!==void 0?i.useSyncExternalStore:b,vs}var Ih;function np(){return Ih||(Ih=1,ys.exports=ep()),ys.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tm;function lp(){if(tm)return ms;tm=1;var i=hu(),s=np();function c(m,b){return m===b&&(m!==0||1/m===1/b)||m!==m&&b!==b}var f=typeof Object.is=="function"?Object.is:c,r=s.useSyncExternalStore,d=i.useRef,v=i.useEffect,p=i.useMemo,y=i.useDebugValue;return ms.useSyncExternalStoreWithSelector=function(m,b,g,_,M){var A=d(null);if(A.current===null){var U={hasValue:!1,value:null};A.current=U}else U=A.current;A=p(function(){function j(lt){if(!at){if(at=!0,X=lt,lt=_(lt),M!==void 0&&U.hasValue){var et=U.value;if(M(et,lt))return J=et}return J=lt}if(et=J,f(X,lt))return et;var nt=_(lt);return M!==void 0&&M(et,nt)?(X=lt,et):(X=lt,J=nt)}var at=!1,X,J,V=g===void 0?null:g;return[function(){return j(b())},V===null?void 0:function(){return j(V())}]},[b,g,_,M]);var H=r(m,A[0],A[1]);return v(function(){U.hasValue=!0,U.value=H},[H]),y(H),H},ms}var em;function ap(){return em||(em=1,hs.exports=lp()),hs.exports}var up=ap();function ip(i,s=c=>c){return up.useSyncExternalStoreWithSelector(i.subscribe,()=>i.state,()=>i.state,s,cp)}function cp(i,s){if(Object.is(i,s))return!0;if(typeof i!="object"||i===null||typeof s!="object"||s===null)return!1;if(i instanceof Map&&s instanceof Map){if(i.size!==s.size)return!1;for(const[f,r]of i)if(!s.has(f)||!Object.is(r,s.get(f)))return!1;return!0}if(i instanceof Set&&s instanceof Set){if(i.size!==s.size)return!1;for(const f of i)if(!s.has(f))return!1;return!0}if(i instanceof Date&&s instanceof Date)return i.getTime()===s.getTime();const c=Object.keys(i);if(c.length!==Object.keys(s).length)return!1;for(let f=0;f<c.length;f++)if(!Object.prototype.hasOwnProperty.call(s,c[f])||!Object.is(i[c[f]],s[c[f]]))return!1;return!0}const gs=ut.createContext(null);function Rm(){return typeof document>"u"?gs:window.__TSR_ROUTER_CONTEXT__?window.__TSR_ROUTER_CONTEXT__:(window.__TSR_ROUTER_CONTEXT__=gs,gs)}function xe(i){const s=ut.useContext(Rm());return i==null||i.warn,s}function ue(i){const s=xe({warn:(i==null?void 0:i.router)===void 0}),c=(i==null?void 0:i.router)||s,f=ut.useRef(void 0);return ip(c.__store,r=>{if(i!=null&&i.select){if(i.structuralSharing??c.options.defaultStructuralSharing){const d=Oe(f.current,i.select(r));return f.current=d,d}return i.select(r)}return r})}const Vi=ut.createContext(void 0),fp=ut.createContext(void 0);function De(i){const s=ut.useContext(i.from?fp:Vi);return ue({select:f=>{const r=f.matches.find(d=>i.from?i.from===d.routeId:d.id===s);if(un(!((i.shouldThrow??!0)&&!r),`Could not find ${i.from?`an active match from "${i.from}"`:"a nearest match!"}`),r!==void 0)return i.select?i.select(r):r},structuralSharing:i.structuralSharing})}function Cs(i){return De({from:i.from,strict:i.strict,structuralSharing:i.structuralSharing,select:s=>i.select?i.select(s.loaderData):s.loaderData})}function Us(i){const{select:s,...c}=i;return De({...c,select:f=>s?s(f.loaderDeps):f.loaderDeps})}function Ls(i){return De({from:i.from,strict:i.strict,shouldThrow:i.shouldThrow,structuralSharing:i.structuralSharing,select:s=>i.select?i.select(s.params):s.params})}function Ns(i){return De({from:i.from,strict:i.strict,shouldThrow:i.shouldThrow,structuralSharing:i.structuralSharing,select:s=>i.select?i.select(s.search):s.search})}function Bs(i){const{navigate:s,state:c}=xe(),f=De({strict:!1,select:r=>r.index});return ut.useCallback(r=>{const d=r.from??(i==null?void 0:i.from)??c.matches[f].fullPath;return s({...r,from:d})},[i==null?void 0:i.from,s])}var sp=sm();const xi=typeof window<"u"?ut.useLayoutEffect:ut.useEffect;function ps(i){const s=ut.useRef({value:i,prev:null}),c=s.current.value;return i!==c&&(s.current={value:i,prev:c}),s.current.prev}function op(i,s,c={},f={}){ut.useEffect(()=>{if(!i.current||f.disabled||typeof IntersectionObserver!="function")return;const r=new IntersectionObserver(([d])=>{s(d)},c);return r.observe(i.current),()=>{r.disconnect()}},[s,c,f.disabled,i])}function rp(i){const s=ut.useRef(null);return ut.useImperativeHandle(i,()=>s.current,[]),s}function dp(i,s){const c=xe(),[f,r]=ut.useState(!1),d=ut.useRef(!1),v=rp(s),{activeProps:p,inactiveProps:y,activeOptions:m,to:b,preload:g,preloadDelay:_,hashScrollIntoView:M,replace:A,startTransition:U,resetScroll:H,viewTransition:j,children:at,target:X,disabled:J,style:V,className:lt,onClick:et,onFocus:nt,onMouseEnter:ht,onMouseLeave:Z,onTouchStart:it,ignoreBlocker:mt,params:Gt,search:Dt,hash:Lt,state:C,mask:Y,reloadDocument:P,unsafeRelative:St,from:R,_fromLocation:q,...w}=i,G=ut.useMemo(()=>{try{return new URL(b),"external"}catch{}return"internal"},[b]),k=ue({select:Ot=>Ot.location.search,structuralSharing:!0}),st=De({strict:!1,select:Ot=>i.from??Ot.fullPath}),W=ut.useMemo(()=>c.buildLocation({...i,from:st}),[c,k,i._fromLocation,st,i.hash,i.to,i.search,i.params,i.state,i.mask,i.unsafeRelative]),Zt=G==="external",gt=i.reloadDocument||Zt?!1:g??c.options.defaultPreload,ye=_??c.options.defaultPreloadDelay??0,Nn=ue({select:Ot=>{if(Zt)return!1;if(m!=null&&m.exact){if(!ig(Ot.location.pathname,W.pathname,c.basepath))return!1}else{const Kt=Ni(Ot.location.pathname,c.basepath),Ne=Ni(W.pathname,c.basepath);if(!(Kt.startsWith(Ne)&&(Kt.length===Ne.length||Kt[Ne.length]==="/")))return!1}return((m==null?void 0:m.includeSearch)??!0)&&!kl(Ot.location.search,W.search,{partial:!(m!=null&&m.exact),ignoreUndefined:!(m!=null&&m.explicitUndefined)})?!1:m!=null&&m.includeHash?Ot.location.hash===W.hash:!0}}),ve=ut.useCallback(()=>{c.preloadRoute({...i,from:st}).catch(Ot=>{console.warn(Ot),console.warn(kg)})},[c,i.to,i._fromLocation,st,i.search,i.hash,i.params,i.state,i.mask,i.unsafeRelative,i.hashScrollIntoView,i.href,i.ignoreBlocker,i.reloadDocument,i.replace,i.resetScroll,i.viewTransition]),ta=ut.useCallback(Ot=>{Ot!=null&&Ot.isIntersecting&&ve()},[ve]);if(op(v,ta,gp,{disabled:!!J||gt!=="viewport"}),ut.useEffect(()=>{d.current||!J&&gt==="render"&&(ve(),d.current=!0)},[J,ve,gt]),Zt)return{...w,ref:v,type:G,href:b,...at&&{children:at},...X&&{target:X},...J&&{disabled:J},...V&&{style:V},...lt&&{className:lt},...et&&{onClick:et},...nt&&{onFocus:nt},...ht&&{onMouseEnter:ht},...Z&&{onMouseLeave:Z},...it&&{onTouchStart:it}};const ea=Ot=>{if(!J&&!pp(Ot)&&!Ot.defaultPrevented&&(!X||X==="_self")&&Ot.button===0){Ot.preventDefault(),sp.flushSync(()=>{r(!0)});const Kt=c.subscribe("onResolved",()=>{Kt(),r(!1)});c.navigate({...i,from:st,replace:A,resetScroll:H,hashScrollIntoView:M,startTransition:U,viewTransition:j,ignoreBlocker:mt})}},ul=Ot=>{J||gt&&ve()},Xi=ul,Qi=Ot=>{if(!(J||!gt))if(!ye)ve();else{const Kt=Ot.target;if(lu.has(Kt))return;const Ne=setTimeout(()=>{lu.delete(Kt),ve()},ye);lu.set(Kt,Ne)}},ge=Ot=>{if(J||!gt||!ye)return;const Kt=Ot.target,Ne=lu.get(Kt);Ne&&(clearTimeout(Ne),lu.delete(Kt))},il=Nn?el(p,{})??hp:Ss,Bn=Nn?Ss:el(y,{})??Ss,na=[lt,il.className,Bn.className].filter(Boolean).join(" "),Hn=(V||il.style||Bn.style)&&{...V,...il.style,...Bn.style};return{...w,...il,...Bn,href:J?void 0:W.maskedLocation?c.history.createHref(W.maskedLocation.href):c.history.createHref(W.href),ref:v,onClick:au([et,ea]),onFocus:au([nt,ul]),onMouseEnter:au([ht,Qi]),onMouseLeave:au([Z,ge]),onTouchStart:au([it,Xi]),disabled:!!J,target:X,...Hn&&{style:Hn},...na&&{className:na},...J&&mp,...Nn&&yp,...f&&vp}}const Ss={},hp={className:"active"},mp={role:"link","aria-disabled":!0},yp={"data-status":"active","aria-current":"page"},vp={"data-transitioning":"transitioning"},lu=new WeakMap,gp={rootMargin:"100px"},au=i=>s=>{i.filter(Boolean).forEach(c=>{s.defaultPrevented||c(s)})},su=ut.forwardRef((i,s)=>{const{_asChild:c,...f}=i,{type:r,ref:d,...v}=dp(f,s),p=typeof f.children=="function"?f.children({isActive:v["data-status"]==="active"}):f.children;return c===void 0&&delete v.disabled,ut.createElement(c||"a",{...v,ref:d},p)});function pp(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}let Sp=class extends bm{constructor(s){super(s),this.useMatch=c=>De({select:c==null?void 0:c.select,from:this.id,structuralSharing:c==null?void 0:c.structuralSharing}),this.useRouteContext=c=>De({...c,from:this.id,select:f=>c!=null&&c.select?c.select(f.context):f.context}),this.useSearch=c=>Ns({select:c==null?void 0:c.select,structuralSharing:c==null?void 0:c.structuralSharing,from:this.id}),this.useParams=c=>Ls({select:c==null?void 0:c.select,structuralSharing:c==null?void 0:c.structuralSharing,from:this.id}),this.useLoaderDeps=c=>Us({...c,from:this.id}),this.useLoaderData=c=>Cs({...c,from:this.id}),this.useNavigate=()=>Bs({from:this.fullPath}),this.Link=ou.forwardRef((c,f)=>Q.jsx(su,{ref:f,from:this.fullPath,...c})),this.$$typeof=Symbol.for("react.memo")}};function _p(i){return new Sp(i)}class bp extends Pg{constructor(s){super(s),this.useMatch=c=>De({select:c==null?void 0:c.select,from:this.id,structuralSharing:c==null?void 0:c.structuralSharing}),this.useRouteContext=c=>De({...c,from:this.id,select:f=>c!=null&&c.select?c.select(f.context):f.context}),this.useSearch=c=>Ns({select:c==null?void 0:c.select,structuralSharing:c==null?void 0:c.structuralSharing,from:this.id}),this.useParams=c=>Ls({select:c==null?void 0:c.select,structuralSharing:c==null?void 0:c.structuralSharing,from:this.id}),this.useLoaderDeps=c=>Us({...c,from:this.id}),this.useLoaderData=c=>Cs({...c,from:this.id}),this.useNavigate=()=>Bs({from:this.fullPath}),this.Link=ou.forwardRef((c,f)=>Q.jsx(su,{ref:f,from:this.fullPath,...c})),this.$$typeof=Symbol.for("react.memo")}}function Rp(i){return new bp(i)}function du(i){return typeof i=="object"?new nm(i,{silent:!0}).createRoute(i):new nm(i,{silent:!0}).createRoute}class nm{constructor(s,c){this.path=s,this.createRoute=f=>{this.silent;const r=_p(f);return r.isRoot=!1,r},this.silent=c==null?void 0:c.silent}}class lm{constructor(s){this.useMatch=c=>De({select:c==null?void 0:c.select,from:this.options.id,structuralSharing:c==null?void 0:c.structuralSharing}),this.useRouteContext=c=>De({from:this.options.id,select:f=>c!=null&&c.select?c.select(f.context):f.context}),this.useSearch=c=>Ns({select:c==null?void 0:c.select,structuralSharing:c==null?void 0:c.structuralSharing,from:this.options.id}),this.useParams=c=>Ls({select:c==null?void 0:c.select,structuralSharing:c==null?void 0:c.structuralSharing,from:this.options.id}),this.useLoaderDeps=c=>Us({...c,from:this.options.id}),this.useLoaderData=c=>Cs({...c,from:this.options.id}),this.useNavigate=()=>{const c=xe();return Bs({from:c.routesById[this.options.id].fullPath})},this.options=s,this.$$typeof=Symbol.for("react.memo")}}function am(i){return typeof i=="object"?new lm(i):s=>new lm({id:i,...s})}function Hs(i,s){let c,f,r,d;const v=()=>(c||(c=i().then(y=>{c=void 0,f=y[s]}).catch(y=>{if(r=y,ug(r)&&r instanceof Error&&typeof window<"u"&&typeof sessionStorage<"u"){const m=`tanstack_router_reload:${r.message}`;sessionStorage.getItem(m)||(sessionStorage.setItem(m,"1"),d=!0)}})),c),p=function(m){if(d)throw window.location.reload(),new Promise(()=>{});if(r)throw r;if(!f)throw v();return ut.createElement(f,m)};return p.preload=v,p}function Ep(){const i=xe(),s=ut.useRef({router:i,mounted:!1}),[c,f]=ut.useState(!1),{hasPendingMatches:r,isLoading:d}=ue({select:g=>({isLoading:g.isLoading,hasPendingMatches:g.matches.some(_=>_.status==="pending")}),structuralSharing:!0}),v=ps(d),p=d||c||r,y=ps(p),m=d||r,b=ps(m);return i.startTransition=g=>{f(!0),ut.startTransition(()=>{g(),f(!1)})},ut.useEffect(()=>{const g=i.history.subscribe(i.load),_=i.buildLocation({to:i.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});return Fl(i.latestLocation.href)!==Fl(_.href)&&i.commitLocation({..._,replace:!0}),()=>{g()}},[i,i.history]),xi(()=>{if(typeof window<"u"&&i.ssr||s.current.router===i&&s.current.mounted)return;s.current={router:i,mounted:!0},(async()=>{try{await i.load()}catch(_){console.error(_)}})()},[i]),xi(()=>{v&&!d&&i.emit({type:"onLoad",...nl(i.state)})},[v,i,d]),xi(()=>{b&&!m&&i.emit({type:"onBeforeRouteMount",...nl(i.state)})},[m,b,i]),xi(()=>{y&&!p&&(i.emit({type:"onResolved",...nl(i.state)}),i.__store.setState(g=>({...g,status:"idle",resolvedLocation:g.location})),Eg(i))},[p,y,i]),null}function Tp(i){const s=ue({select:c=>`not-found-${c.location.pathname}-${c.status}`});return Q.jsx(xs,{getResetKey:()=>s,onCatch:(c,f)=>{var r;if(Ve(c))(r=i.onCatch)==null||r.call(i,c,f);else throw c},errorComponent:({error:c})=>{var f;if(Ve(c))return(f=i.fallback)==null?void 0:f.call(i,c);throw c},children:i.children})}function Mp(){return Q.jsx("p",{children:"Not Found"})}function Kl(i){return Q.jsx(Q.Fragment,{children:i.children})}function Em(i,s,c){return s.options.notFoundComponent?Q.jsx(s.options.notFoundComponent,{data:c}):i.options.defaultNotFoundComponent?Q.jsx(i.options.defaultNotFoundComponent,{data:c}):Q.jsx(Mp,{})}function Ap({children:i}){return typeof document<"u"?null:Q.jsx("script",{className:"$tsr",dangerouslySetInnerHTML:{__html:[i].filter(Boolean).join(`
`)}})}function Op(){const i=xe(),c=(i.options.getScrollRestorationKey||Ms)(i.latestLocation),f=c!==Ms(i.latestLocation)?c:void 0;if(!i.isScrollRestoring||!i.isServer)return null;const r={storageKey:Hi,shouldScrollRestoration:!0};return f&&(r.key=f),Q.jsx(Ap,{children:`(${hm.toString()})(${JSON.stringify(r)})`})}const Tm=ut.memo(function({matchId:s}){var c,f;const r=xe(),d=ue({select:X=>{const J=X.matches.find(V=>V.id===s);return un(J),{routeId:J.routeId,ssr:J.ssr,_displayPending:J._displayPending}},structuralSharing:!0}),v=r.routesById[d.routeId],p=v.options.pendingComponent??r.options.defaultPendingComponent,y=p?Q.jsx(p,{}):null,m=v.options.errorComponent??r.options.defaultErrorComponent,b=v.options.onCatch??r.options.defaultOnCatch,g=v.isRoot?v.options.notFoundComponent??((c=r.options.notFoundRoute)==null?void 0:c.options.component):v.options.notFoundComponent,_=d.ssr===!1||d.ssr==="data-only",M=(!v.isRoot||v.options.wrapInSuspense||_)&&(v.options.wrapInSuspense??p??(((f=v.options.errorComponent)==null?void 0:f.preload)||_))?ut.Suspense:Kl,A=m?xs:Kl,U=g?Tp:Kl,H=ue({select:X=>X.loadedAt}),j=ue({select:X=>{var J;const V=X.matches.findIndex(lt=>lt.id===s);return(J=X.matches[V-1])==null?void 0:J.routeId}}),at=v.isRoot?v.options.shellComponent??Kl:Kl;return Q.jsxs(at,{children:[Q.jsx(Vi.Provider,{value:s,children:Q.jsx(M,{fallback:y,children:Q.jsx(A,{getResetKey:()=>H,errorComponent:m||Gi,onCatch:(X,J)=>{if(Ve(X))throw X;b==null||b(X,J)},children:Q.jsx(U,{fallback:X=>{if(!g||X.routeId&&X.routeId!==d.routeId||!X.routeId&&!v.isRoot)throw X;return ut.createElement(g,X)},children:_||d._displayPending?Q.jsx(Fg,{fallback:y,children:Q.jsx(um,{matchId:s})}):Q.jsx(um,{matchId:s})})})})}),j===ze&&r.options.scrollRestoration?Q.jsxs(Q.Fragment,{children:[Q.jsx(zp,{}),Q.jsx(Op,{})]}):null]})});function zp(){const i=xe(),s=ut.useRef(void 0);return Q.jsx("script",{suppressHydrationWarning:!0,ref:c=>{c&&(s.current===void 0||s.current.href!==i.latestLocation.href)&&(i.emit({type:"onRendered",...nl(i.state)}),s.current=i.latestLocation)}},i.latestLocation.state.__TSR_key)}const um=ut.memo(function({matchId:s}){var c,f,r,d;const v=xe(),{match:p,key:y,routeId:m}=ue({select:_=>{const M=_.matches.find(at=>at.id===s),A=M.routeId,U=v.routesById[A].options.remountDeps??v.options.defaultRemountDeps,H=U==null?void 0:U({routeId:A,loaderDeps:M.loaderDeps,params:M._strictParams,search:M._strictSearch});return{key:H?JSON.stringify(H):void 0,routeId:A,match:{id:M.id,status:M.status,error:M.error,_forcePending:M._forcePending,_displayPending:M._displayPending}}},structuralSharing:!0}),b=v.routesById[m],g=ut.useMemo(()=>{const _=b.options.component??v.options.defaultComponent;return _?Q.jsx(_,{},y):Q.jsx(Mm,{})},[y,b.options.component,v.options.defaultComponent]);if(p._displayPending)throw(c=v.getMatch(p.id))==null?void 0:c._nonReactive.displayPendingPromise;if(p._forcePending)throw(f=v.getMatch(p.id))==null?void 0:f._nonReactive.minPendingPromise;if(p.status==="pending"){const _=b.options.pendingMinMs??v.options.defaultPendingMinMs;if(_){const M=v.getMatch(p.id);if(M&&!M._nonReactive.minPendingPromise&&!v.isServer){const A=Pl();M._nonReactive.minPendingPromise=A,setTimeout(()=>{A.resolve(),M._nonReactive.minPendingPromise=void 0},_)}}throw(r=v.getMatch(p.id))==null?void 0:r._nonReactive.loadPromise}if(p.status==="notFound")return un(Ve(p.error)),Em(v,b,p.error);if(p.status==="redirected")throw un(Ge(p.error)),(d=v.getMatch(p.id))==null?void 0:d._nonReactive.loadPromise;if(p.status==="error"){if(v.isServer){const _=(b.options.errorComponent??v.options.defaultErrorComponent)||Gi;return Q.jsx(_,{error:p.error,reset:void 0,info:{componentStack:""}})}throw p.error}return g}),Mm=ut.memo(function(){const s=xe(),c=ut.useContext(Vi),f=ue({select:m=>{var b;return(b=m.matches.find(g=>g.id===c))==null?void 0:b.routeId}}),r=s.routesById[f],d=ue({select:m=>{const g=m.matches.find(_=>_.id===c);return un(g),g.globalNotFound}}),v=ue({select:m=>{var b;const g=m.matches,_=g.findIndex(M=>M.id===c);return(b=g[_+1])==null?void 0:b.id}}),p=s.options.defaultPendingComponent?Q.jsx(s.options.defaultPendingComponent,{}):null;if(d)return Em(s,r,void 0);if(!v)return null;const y=Q.jsx(Tm,{matchId:v});return c===ze?Q.jsx(ut.Suspense,{fallback:p,children:y}):y});function Dp(){const i=xe(),s=i.options.defaultPendingComponent?Q.jsx(i.options.defaultPendingComponent,{}):null,c=i.isServer||typeof document<"u"&&i.ssr?Kl:ut.Suspense,f=Q.jsxs(c,{fallback:s,children:[!i.isServer&&Q.jsx(Ep,{}),Q.jsx(xp,{})]});return i.options.InnerWrap?Q.jsx(i.options.InnerWrap,{children:f}):f}function xp(){const i=xe(),s=ue({select:r=>{var d;return(d=r.matches[0])==null?void 0:d.id}}),c=ue({select:r=>r.loadedAt}),f=s?Q.jsx(Tm,{matchId:s}):null;return Q.jsx(Vi.Provider,{value:s,children:i.options.disableGlobalCatchBoundary?f:Q.jsx(xs,{getResetKey:()=>c,errorComponent:Gi,onCatch:r=>{r.message||r.toString()},children:f})})}const Cp=i=>new Up(i);class Up extends qg{constructor(s){super(s)}}typeof globalThis<"u"?(globalThis.createFileRoute=du,globalThis.createLazyFileRoute=am):typeof window<"u"&&(window.createFileRoute=du,window.createLazyFileRoute=am);function Lp({router:i,children:s,...c}){Object.keys(c).length>0&&i.update({...i.options,...c,context:{...i.options.context,...c.context}});const f=Rm(),r=Q.jsx(f.Provider,{value:i,children:s});return i.options.Wrap?Q.jsx(i.options.Wrap,{children:r}):r}function Np({router:i,...s}){return Q.jsx(Lp,{router:i,...s,children:Q.jsx(Dp,{})})}const Bp="modulepreload",Hp=function(i){return"/"+i},im={},qs=function(s,c,f){let r=Promise.resolve();if(c&&c.length>0){document.getElementsByTagName("link");const v=document.querySelector("meta[property=csp-nonce]"),p=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));r=Promise.allSettled(c.map(y=>{if(y=Hp(y),y in im)return;im[y]=!0;const m=y.endsWith(".css"),b=m?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${y}"]${b}`))return;const g=document.createElement("link");if(g.rel=m?"stylesheet":Bp,m||(g.as="script"),g.crossOrigin="",g.href=y,p&&g.setAttribute("nonce",p),document.head.appendChild(g),m)return new Promise((_,M)=>{g.addEventListener("load",_),g.addEventListener("error",()=>M(new Error(`Unable to preload CSS for ${y}`)))})}))}function d(v){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=v,window.dispatchEvent(p),!p.defaultPrevented)throw v}return r.then(v=>{for(const p of v||[])p.status==="rejected"&&d(p.reason);return s().catch(d)})},qp=function(){return null},wi=Rp({component:()=>Q.jsxs(Q.Fragment,{children:[Q.jsxs("div",{className:"p-2 flex gap-2",children:[Q.jsx(su,{to:"/",className:"[&.active]:font-bold",children:"Home"})," ",Q.jsx(su,{to:"/about",className:"[&.active]:font-bold",children:"About"}),Q.jsx(su,{to:"/upload",className:"[&.active]:font-bold",children:"Upload a File"})]}),Q.jsx("hr",{}),Q.jsx(Mm,{}),Q.jsx(qp,{})]})}),jp=()=>qs(()=>import("./upload-CPuyUdnz.js"),[]),Yp=du("/upload")({component:Hs(jp,"component")}),Gp=()=>qs(()=>import("./about-CDeeQZ5i.js"),[]),Vp=du("/about")({component:Hs(Gp,"component")}),wp=()=>qs(()=>import("./index-D4OXCbNC.js"),[]),Xp=du("/")({component:Hs(wp,"component")}),Qp=Yp.update({id:"/upload",path:"/upload",getParentRoute:()=>wi}),Zp=Vp.update({id:"/about",path:"/about",getParentRoute:()=>wi}),Kp=Xp.update({id:"/",path:"/",getParentRoute:()=>wi}),Jp={IndexRoute:Kp,AboutRoute:Zp,UploadRoute:Qp},$p=wi._addFileChildren(Jp)._addFileTypes(),kp=Cp({routeTree:$p}),cm=document.getElementById("root");cm.innerHTML||Wv.createRoot(cm).render(Q.jsx(ut.StrictMode,{children:Q.jsx(Np,{router:kp})}));export{Q as j,ut as r};
